package com.scbs.cis.mapper;

import com.scbs.cis.entity.TaskState;
import com.scbs.cis.model.request.taskMonitoring.TaskStateRequest;
import com.scbs.cis.model.response.taskMonitoring.TaskStateDetailResponse;
import java.util.List;

import org.mapstruct.*;

@Mapper
public interface TaskStateMapper {

    @Mapping(target = "taskStateId", source = "id")
    TaskStateDetailResponse toTaskStateDetailResponse(TaskState taskState);

    @Named("toTaskStateDetailResponseIgnoringFields")
    @Mapping(target = "taskStateId", source = "id")
    @Mapping(target = "requestMessage", ignore = true)
    @Mapping(target = "responseMessage", ignore = true)
    @Mapping(target = "retryableFlag", ignore = true)
    TaskStateDetailResponse toTaskStateDetailResponseIgnoringFields(TaskState taskState);

    @Named("toTaskStatesDetailResponse")
    @IterableMapping(qualifiedByName = "toTaskStateDetailResponseIgnoringFields")
    List<TaskStateDetailResponse> toListOfTaskStateDetailResponseIgnoringFields(List<TaskState> taskStates);

    @Mapping(target = "id", source = "taskStateId")
    TaskState toTaskState(TaskStateRequest taskStateRequest);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    void updateTaskStateFromTaskStateRequest(TaskStateRequest source, @MappingTarget TaskState target);
}
