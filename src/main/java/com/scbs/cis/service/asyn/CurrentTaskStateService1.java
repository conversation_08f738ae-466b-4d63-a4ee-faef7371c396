package com.scbs.cis.service.asyn;

import com.scbs.cis.constants.StatusResponseEnum;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.invx.cis.toolbox.logger.Log;
import com.invx.cis.toolbox.logger.LogFactory;

import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;

@Service("currentTaskStateService1")
public class CurrentTaskStateService1 implements CurrentTaskStateService {

    private final Log log = LogFactory.getLogger(this.getClass());


    @Async
    public CompletableFuture<StatusResponseEnum> process() throws Exception {
        sleep1();
        sleep2();
        sleep3();
        return null;
    }

    @Async
    public CompletableFuture<StatusResponseEnum> sleep1() throws Exception {

        String start = LocalDateTime.now().toString();
        Thread.sleep(1000);
        String end = LocalDateTime.now().toString();
        log.info("currentTaskStateService1-sleep1 : Thread-name {} :Start {} ,End {} ",Thread.currentThread().getName(),start,end);
        return CompletableFuture.completedFuture(StatusResponseEnum.SUCCESS);
    }

    @Async
    public CompletableFuture<StatusResponseEnum> sleep2() throws Exception {
        String start = LocalDateTime.now().toString();
        Thread.sleep(2000);
        String end = LocalDateTime.now().toString();
        log.info("currentTaskStateService1-sleep2 : Thread-name {} :Start {} ,End {} ",Thread.currentThread().getName(),start,end);
        return CompletableFuture.completedFuture(StatusResponseEnum.SUCCESS);
    }

    @Async
    public CompletableFuture<StatusResponseEnum> sleep3() throws Exception {
        String start = LocalDateTime.now().toString();
        Thread.sleep(3000);
        String end = LocalDateTime.now().toString();
        log.info("currentTaskStateService1-sleep3 :Thread-name {} :Start {} ,End {} ",Thread.currentThread().getName(),start,end);
        return CompletableFuture.completedFuture(StatusResponseEnum.SUCCESS);

    }

    @Async
    public CompletableFuture<StatusResponseEnum> sleep4() throws Exception {
        String start = LocalDateTime.now().toString();
        Thread.sleep(4000);
        String end = LocalDateTime.now().toString();
        log.info("currentTaskStateService1-sleep3 :Thread-name {} :Start {} ,End {} ",Thread.currentThread().getName(),start,end);
        return CompletableFuture.completedFuture(StatusResponseEnum.SUCCESS);

    }

    @Async
    public CompletableFuture<StatusResponseEnum> sleep5() throws Exception {
        String start = LocalDateTime.now().toString();
        Thread.sleep(5000);
        String end = LocalDateTime.now().toString();
        log.info("currentTaskStateService1-sleep5 :Thread-name {} :Start {} ,End {} ",Thread.currentThread().getName(),start,end);
        return CompletableFuture.completedFuture(StatusResponseEnum.SUCCESS);

    }
}
