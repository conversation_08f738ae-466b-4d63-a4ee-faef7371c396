package com.scbs.cis.service.CisKyc;


import com.scbs.cis.constants.CisConstants;
import com.scbs.cis.constants.StatusResponseEnum;
import com.scbs.cis.exception.CISServiceException;
import com.scbs.cis.model.RequestCommonHeaderModel;
import com.scbs.cis.model.request.cisKyc.CalculateCustomerKycRequest;
import com.scbs.cis.model.response.cisKyc.getcalKYC.CalcKYCResponseEntity;
import com.scbs.cis.utils.CommonUtil;
import com.scbs.cis.utils.ExternalApiUtils;

import java.time.LocalDateTime;
import java.util.Objects;

import com.scbs.cis.utils.ValidateUtil;
import com.scbs.cis.utils.VariableConfigUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import com.invx.cis.toolbox.logger.Log;
import com.invx.cis.toolbox.logger.LogFactory;

@Service
@RequiredArgsConstructor
public class CisKycService {

    private final ExternalApiUtils externalApiUtils;
    @Value("${cis.api.calculate.kyc}")
    private String calCustomerKycUrl;

    @Value("${max_retry.kyc}")
    public Integer maxRetryKyc;

    private final VariableConfigUtil variableConfigUtil;

    private final Log log = LogFactory.getLogger(this.getClass());

    public CalcKYCResponseEntity calCustomerKyc(RequestCommonHeaderModel requestCommonHeaderModel,
                                                CalculateCustomerKycRequest calculateCustomerKycRequest) throws CISServiceException {

        for (int i = 0; i < maxRetryKyc; i++) {
            log.info("[calCustomerKyc try at : {} ,retry dateTime {} ]",i, LocalDateTime.now());
            try {
                ResponseEntity<CalcKYCResponseEntity> responseEntity = externalApiUtils.restAPICallerCisKyc(calCustomerKycUrl,
                        HttpMethod.POST, this.headerCisKyc(requestCommonHeaderModel), calculateCustomerKycRequest, CalcKYCResponseEntity.class);
                return Objects.requireNonNull(responseEntity).getBody();

            } catch (CISServiceException ex) {

                Boolean retryableFlag = variableConfigUtil.checkIsRetry(String.valueOf(ex.getStatusResponse().getCode()));

                if (BooleanUtils.isFalse(retryableFlag) || i == maxRetryKyc - 1) {
                    throw ex;
                }

            } catch (Exception ex) {
                throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR, StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED);
            }

        }

        return new CalcKYCResponseEntity();
    }

    private HttpHeaders headerCisKyc(RequestCommonHeaderModel headerModel) {

        HttpHeaders headers = new HttpHeaders();
        headers.set(CisConstants.HEADER_CONTENT_TYPE, CisConstants.CONTENT_TYPE_APPLICATION_JSON);
        headers.set(CisConstants.HEADER_REQUEST_UUID, CommonUtil.generateUUID());
        headers.set(CisConstants.HEADER_CORRELATION_ID, headerModel.getCorrelationId());
        headers.set(CisConstants.HEADER_RESOURCE_OWNER_ID, headerModel.getResourceOwnerId());
        headers.set(CisConstants.HEADER_APPLICATION_REFERENCE, headerModel.getApplicationReference());
        headers.set(CisConstants.HEADER_SOURCE_SYSTEM, "CIS_MANAGEMENT");
        headers.set(CisConstants.HEADER_CIS_UID, headerModel.getCisUid());
        headers.set(CisConstants.HEADER_CUSTOMER_ID, headerModel.getCustomerId());
        return headers;
    }
}
