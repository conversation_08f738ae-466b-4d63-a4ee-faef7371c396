package com.scbs.cis.service.kafka;

import com.scbs.cis.service.openInvestmentDrx.OpenInvestmentDrxDoneService;
import com.scbs.cis.service.openInvestmentFund.OpenInvestmentFundDoneService;
import com.scbs.cis.service.openInvestmentOffshore.OpenInvestmentOffshoreDoneService;
import com.scbs.cis.service.openInvestmentOnshore.OpenInvestmentOnshoreDoneService;
import com.scbs.cis.service.openInvestmentServiceGtn.OpenInvestmentServiceGtnDoneService;
import com.scbs.cis.service.openInvestmentServiceSaxo.OpenInvestmentServiceSaxoDoneService;
import com.scbs.cis.service.openInvestmentTfex.OpenInvestmentTfexDoneService;
import com.scbs.cis.service.openInvestmentWalletProfile.OpenInvestmentWalletProfileDoneService;
import com.scbs.cis.service.updateCustomerAccountToDrx.UpdateCustomerAccountToDrxDoneService;
import com.scbs.cis.service.updateCustomerAccountToFcn.UpdateCustomerAccountToFcnDoneService;
import com.scbs.cis.service.updateCustomerAccountToSba.UpdateCustomerAccountToSbaDoneService;
import com.scbs.cis.service.updateCustomerAccountToTfex.UpdateCustomerAccountToTfexDoneService;
import com.scbs.cis.service.updateCustomerProfileFcn.UpdateCustomerProfileToFcnDoneService;
import com.scbs.cis.service.updateCustomerProfileToDrx.UpdateCustomerProfileToDrxDoneService;
import com.scbs.cis.service.updateCustomerProfileToSba.UpdateCustomerProfileToSbaDoneService;
import com.scbs.cis.service.updateCustomerProfileToTfex.UpdateCustomerProfileToTfexDoneService;
import com.scbs.cis.service.uploadDocumentToFcn.UploadDocumentToFcnDoneService;
import com.scbs.cis.utils.CommonUtil;
import java.util.function.Consumer;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class KafkaConsumer {

    public static final String KAFKA_SUCCESS_MESSAGE = "{kafka}{success} {} message -> {}";
    private static final String TASK_ID = "taskId";
    private static final String TASK_STATE_ID = "taskStateId";

    private final Logger log = LoggerFactory.getLogger(this.getClass());
    private final OpenInvestmentTfexDoneService openInvestmentTfexDoneService;
    private final OpenInvestmentOnshoreDoneService openInvestmentOnshoreDoneService;
    private final OpenInvestmentOffshoreDoneService openInvestmentOffshoreDoneService;
    private final OpenInvestmentDrxDoneService openInvestmentDrxDoneService;
    private final OpenInvestmentFundDoneService openInvestmentFundDoneService;
    private final OpenInvestmentWalletProfileDoneService openInvestmentWalletProfileDoneService;
    private final OpenInvestmentServiceSaxoDoneService openInvestmentServiceSaxoDoneService;
    private final OpenInvestmentServiceGtnDoneService openInvestmentServiceGtnDoneService;
    private final UploadDocumentToFcnDoneService uploadDocumentToFcnDoneService;
    private final UpdateCustomerProfileToFcnDoneService updateCustomerProfileToFcnDoneService;
    private final UpdateCustomerProfileToSbaDoneService updateCustomerProfileToSbaDoneService;
    private final UpdateCustomerProfileToTfexDoneService updateCustomerProfileToTfexDoneService;
    private final UpdateCustomerProfileToDrxDoneService updateCustomerProfileToDrxDoneService;
    private final UpdateCustomerAccountToTfexDoneService updateCustomerAccountToTfexDoneService;
    private final UpdateCustomerAccountToDrxDoneService updateCustomerAccountToDrxDoneService;
    private final UpdateCustomerAccountToFcnDoneService updateCustomerAccountToFcnDoneService;
    private final UpdateCustomerAccountToSbaDoneService updateCustomerAccountToSbaDoneService;

    @KafkaListener(topics = "${kafka.open.investment.tfex.done}", groupId = "${spring.kafka.group.id.config}")
    public void listenerOpenInvestmentTfexDone(ConsumerRecord<String, String> record) {
        this.callToService(record, openInvestmentTfexDoneService::openInvestmentTfexDone);
    }

    @KafkaListener(topics = "${kafka.open.investment.offshore.done}", groupId = "${spring.kafka.group.id.config}")
    public void listenerOpenInvestmentOffshoreDone(ConsumerRecord<String, String> record) {
        this.callToService(record, openInvestmentOffshoreDoneService::openInvestmentOffshoreDone);
    }

    @KafkaListener(topics = "${kafka.open.investment.onshore.done}", groupId = "${spring.kafka.group.id.config}")
    public void listenerOpenInvestmentOnshore(ConsumerRecord<String, String> record) {
        this.callToService(record, openInvestmentOnshoreDoneService::openInvestmentOnshoreDone);
    }

    @KafkaListener(topics = "${kafka.open.investment.drx.done}", groupId = "${spring.kafka.group.id.config}")
    public void listenerOpenInvestmentDrxDone(ConsumerRecord<String, String> record) {
        this.callToService(record, openInvestmentDrxDoneService::openInvestmentDrxDone);
    }

    @KafkaListener(topics = "${kafka.open.investment.fund.done}", groupId = "${spring.kafka.group.id.config}")
    public void listenerOpenInvestmentFundDone(ConsumerRecord<String, String> record) {
        this.callToService(record, openInvestmentFundDoneService::openInvestmentFundDone);
    }

    @KafkaListener(topics = "${kafka.open.investment.wallet.profile.done}", groupId = "${spring.kafka.group.id.config}")
    public void listenerOpenInvestmentWalletProfileDone(ConsumerRecord<String, String> record) {
        this.callToService(record, openInvestmentWalletProfileDoneService::openInvestmentWalletProfileDone);
    }

    @KafkaListener(topics = "${kafka.open.investment.service.saxo.done}", groupId = "${spring.kafka.group.id.config}")
    public void listenerOpenInvestmentServiceSaxoDone(ConsumerRecord<String, String> record) {
        this.callToService(record, openInvestmentServiceSaxoDoneService::openInvestmentServiceSaxoDone);
    }

    @KafkaListener(topics = "${kafka.open.investment.service.gtn.done}", groupId = "${spring.kafka.group.id.config}")
    public void listenerOpenInvestmentServiceGtnDone(ConsumerRecord<String, String> record) {
        this.callToService(record, openInvestmentServiceGtnDoneService::openInvestmentServiceGtnDone);
    }

    @KafkaListener(topics = "${kafka.uploadDocumentToFcnDone}", groupId = "${spring.kafka.group.id.config}")
    public void listenerUploadDocumentToFcnDone(ConsumerRecord<String, String> record) {
        this.callToService(record, uploadDocumentToFcnDoneService::uploadDocumentToFcnDone);
    }

    @KafkaListener(topics = "${kafka.updateCustomerProfileToFcnDone}", groupId = "${spring.kafka.group.id.config}")
    public void listenerUpdateCustomerProfileToFcnDone(ConsumerRecord<String, String> record) {
        this.callToService(record, updateCustomerProfileToFcnDoneService::updateCustomerProfileToFcnDone);
    }

    @KafkaListener(topics = "${kafka.updateCustomerProfileToSbaDone}", groupId = "${spring.kafka.group.id.config}")
    public void listenerUpdateCustomerProfileToSbaDone(ConsumerRecord<String, String> record) {
        this.callToService(record, updateCustomerProfileToSbaDoneService::updateCustomerProfileToSbaDone);
    }

    @KafkaListener(topics = "${kafka.updateCustomerProfileToTfexDone}", groupId = "${spring.kafka.group.id.config}")
    public void listenerUpdateCustomerProfileToTfexDone(ConsumerRecord<String, String> record) {
        this.callToService(record, updateCustomerProfileToTfexDoneService::updateCustomerProfileToTfexDone);
    }

    @KafkaListener(topics = "${kafka.updateCustomerAccountToTfexDone}", groupId = "${spring.kafka.group.id.config}")
    public void listenerUpdateCustomerAccountToTfexDone(ConsumerRecord<String, String> record) {
        this.callToService(record, updateCustomerAccountToTfexDoneService::updateCustomerAccountToTfexDone);
    }

    @KafkaListener(topics = "${kafka.updateCustomerAccountToDrxDone}", groupId = "${spring.kafka.group.id.config}")
    public void listenerUpdateCustomerAccountToDrxDone(ConsumerRecord<String, String> record) {
        this.callToService(record, updateCustomerAccountToDrxDoneService::updateCustomerAccountToDrxDone);
    }

    @KafkaListener(topics = "${kafka.updateCustomerProfileToDrxDone}", groupId = "${spring.kafka.group.id.config}")
    public void listenerUpdateCustomerProfileToDrxDone(ConsumerRecord<String, String> record) {
        this.callToService(record, updateCustomerProfileToDrxDoneService::updateCustomerProfileToDrxDone);
    }

    @KafkaListener(topics = "${kafka.updateCustomerAccountToFcnDone}", groupId = "${spring.kafka.group.id.config}")
    public void listenerUpdateCustomerAccountToFcnDone(ConsumerRecord<String, String> record) {
        this.callToService(record, updateCustomerAccountToFcnDoneService::updateCustomerAccountToFcnDone);
    }

    @SneakyThrows
    @KafkaListener(topics = "${kafka.updateCustomerAccountToSbaDone}", groupId = "${spring.kafka.group.id.config}")
    public void listenerUpdateCustomerAccountToSbaDone(ConsumerRecord<String, String> record) {
        this.callToService(record, updateCustomerAccountToSbaDoneService::updateCustomerAccountToSbaDone);
    }

    private void callToService(ConsumerRecord<String, String> record, Consumer<String> function) {
        String message = record.value();
        String topic = record.topic();
        try {
            this.setupHeader(record);
            log.info(KAFKA_SUCCESS_MESSAGE, topic, message);
            function.accept(message);
        } catch (Exception ex) {
            log.info("topicName {} errors : {}", topic, ex.toString());
            log.error(CommonUtil.convertStackToStingNotLimit(ex));
        }
    }

    private void setupHeader(ConsumerRecord<String, String> kafkaMessage) {
        ThreadContext.remove(TASK_ID);
        ThreadContext.remove(TASK_STATE_ID);
        if (kafkaMessage.headers() != null) {
            Header taskId = kafkaMessage.headers().lastHeader(TASK_ID);
            if (taskId != null && taskId.value() != null && taskId.value().length > 0) {
                ThreadContext.put(TASK_ID, new String(taskId.value()));
            }
            Header taskStateId = kafkaMessage.headers().lastHeader(TASK_STATE_ID);
            if (taskStateId != null && taskStateId.value() != null && taskStateId.value().length > 0) {
                ThreadContext.put(TASK_STATE_ID, new String(taskStateId.value()));
            }
        }
    }

}
