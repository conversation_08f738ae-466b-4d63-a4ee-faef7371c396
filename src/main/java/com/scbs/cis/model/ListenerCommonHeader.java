package com.scbs.cis.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ListenerCommonHeader {
    @JsonProperty("requestuid")
    private String requestUid;

    @JsonProperty("correlationid")
    private String correlationId;

    @JsonProperty("resourceownerid")
    private String resourceOwnerId;

    @JsonProperty("applicationreference")
    private String applicationReference;

    @JsonProperty("sourcesystem")
    private String sourceSystem;

    @JsonProperty("cisuid")
    private String cisUid;

    @JsonProperty("customerid")
    private String customerId;

}