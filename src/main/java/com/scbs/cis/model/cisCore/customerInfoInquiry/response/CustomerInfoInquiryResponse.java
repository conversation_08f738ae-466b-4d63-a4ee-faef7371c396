package com.scbs.cis.model.cisCore.customerInfoInquiry.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerInfoInquiryResponse {

    @JsonProperty("custProfile")
    private CustomerProfileInquiryResponse customerProfileInquiryResponse;
    @JsonProperty("cusIdList")
    private List<CustomerCisUidInquiryResponse> customerCisUidInquiryResponseList;
    @JsonProperty("addresses")
    private List<CustomerAddressInquiryResponse> customerAddressInquiryResponseList;
    @JsonProperty("custVulnerable")
    private CustomerVulnerableInquiryResponse customerVulnerableInquiryResponse;
    @JsonProperty("custSuitabilities")
    private List<CustomerSuitabilityInquiryResponse> suitabilityInquiryResponseList;
    @JsonProperty("relationships")
    private List<CustomerRelationshipInquiryResponse> customerRelationshipInquiryResponseList;

}
