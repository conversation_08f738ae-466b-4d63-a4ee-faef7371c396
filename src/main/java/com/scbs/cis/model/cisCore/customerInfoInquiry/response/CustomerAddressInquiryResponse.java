package com.scbs.cis.model.cisCore.customerInfoInquiry.response;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scbs.cis.deserializer.LocalDateTimeDeserializer;
import com.scbs.cis.serializer.LocalDateTimeSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerAddressInquiryResponse {

    private String userAddressId;
    private String cisUid;
    private String customerId;
    private String addressType;
    private String sameAs;
    private String no;
    private String building;
    private String floor;
    private String roomNo;
    private String soi;
    private String village;
    private String moo;
    private String road;
    private String subdistrictName;
    private String subdistrictCode;
    private String districtName;
    private String districtCode;
    private String provinceName;
    private String provinceCode;
    private String countryCode;
    private String zipCode;
    private String department;
    private String remark;
    private String createdBy;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createdDatetime;
    private String updatedBy;
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime updatedDatetime;
    private String migratedSource;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime migratedDatetime;

}