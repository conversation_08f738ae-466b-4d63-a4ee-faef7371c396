package com.scbs.cis.model.cisCore.customerInfoInquiry.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scbs.cis.deserializer.*;
import com.scbs.cis.serializer.LocalDateTimeSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RecordCustomerVulnerable {
    @JsonDeserialize(using = NumberDeserializer.class)
    private int order;
    @JsonDeserialize(using = StringDeserializer.class)
    private String customerVulnerableId;
    @JsonDeserialize(using = BooleanDeserializer.class)
    private Boolean isDisabledVul;
    @JsonDeserialize(using = StringDeserializer.class)
    private String remark;
    @JsonDeserialize(using = StringDeserializer.class)
    private String createdBy;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createdDatetime;
    @JsonDeserialize(using = StringDeserializer.class)
    private String updatedBy ;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime updatedDatetime;
    @JsonDeserialize(using = StringDeserializer.class)
    private String migratedSource;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime migratedDatetime;
}
