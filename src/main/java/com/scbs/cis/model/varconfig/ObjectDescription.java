package com.scbs.cis.model.varconfig;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ObjectDescription {
    private String level;

    @JsonProperty("investor_type")
    private String investorType;

    @JsonProperty("th_equity")
    private String thEquity;

    private String fund;

    private String bond;
}
