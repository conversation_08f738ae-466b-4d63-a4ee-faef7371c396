package com.scbs.cis.model.varconfig;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerEmailConfig {

    @JsonProperty("sender_ds")
    private String sendDs;

    @JsonProperty("subject_ds")
    private String subjectDs;

    @JsonProperty("th_equity_information")
    private String thEquityInformation;

    @JsonProperty("offshore_information")
    private String offshoreInformation;

    @JsonProperty("fund_information")
    private String fundInformation;

    @JsonProperty("bond_information")
    private String bondInformation;

    @JsonProperty("da_information")
    private String daInformation;

    @JsonProperty("start_investment")
    private String startInvestment;

    @JsonProperty("invx_online")
    private String invxOnline;

    @JsonProperty("invx_global_trade")
    private String invxGlobalTrade;

    private String facebook;
    private String line;
    private String twitter;
    private String instagram;
    private String youtube;
    private String tiktok;

    @JsonProperty("app_store")
    private String appStore;

    @JsonProperty("google_play")
    private String googlePlay;
}