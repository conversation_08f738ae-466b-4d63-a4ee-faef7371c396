package com.scbs.cis.model;

import jakarta.mail.internet.InternetAddress;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailRequest {

    private String emailTo;
    private String emailCC;
    private String subject;
    private String bodyHtml;
    private InternetAddress senderDetail;
//    private String cisUid;
//    private String applicationDate;
//    private String completedDateTime;
//    private CustomerProfileRequest customerProfile;
//    private CustomerKycRequest customerKycRequest;
//    private SuitabilityRequest customerSuit;

}
