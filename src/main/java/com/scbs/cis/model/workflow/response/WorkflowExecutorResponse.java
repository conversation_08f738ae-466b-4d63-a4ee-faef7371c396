package com.scbs.cis.model.workflow.response;

import com.scbs.cis.constants.StatusResponseEnum;
import com.scbs.cis.model.ErrorModel;
import com.scbs.cis.model.ResponseModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.http.HttpStatus;

import java.util.List;

@Setter@Getter
@AllArgsConstructor
@NoArgsConstructor
public class WorkflowExecutorResponse {
    private HttpStatus status;
    private ResponseModel model;
}
