package com.scbs.cis.model.response.cisCore.customerRelationShip.getCustomerRelationShip;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.BirthDateDeserializer;
import com.scbs.cis.deserializer.LocalDateDeserializer;
import com.scbs.cis.deserializer.LocalDateTimeDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CustomerRelationshipResponseFromCisCore {

    private String customerRelationshipId;
    private String customerId;
    private String relationshipType;
    private String relationshipId;
    private String relationshipOther;
    private String titleId;
    private String titleOtherTh;
    private String titleOtherEn;
    private String firstNameTh;
    private String middleNameTh;
    private String lastNameTh;
    private String firstNameEn;
    private String middleNameEn;
    private String lastNameEn;
    @JsonDeserialize(using = BirthDateDeserializer.class)
    private LocalDate birthDate;
    private String gender;
    private String nationality;
    private String nationalityOther;
    private String cardType;
    private String cardNumber;
    private String cardIssuingCountry;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate cardIssueDate;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate cardExpiryDate;
    private String mobileTelNo;
    private String telephoneNo;
    private String officeTelephoneNo;
    private String contactFaxNo;
    private String officeFaxNo;
    private String emailAddress;
    private String officeAddress;
    private String address1;
    private String address2;
    private String address3;
    private String address4;
    private String documentLanguage;
    private Boolean politicalPersonFlag;
    private String politicalPosition;
    private String createdBy;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createdDatetime;
    private String updatedBy;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime updatedDatetime;
    private String migratedSource;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime migratedDatetime;

}
