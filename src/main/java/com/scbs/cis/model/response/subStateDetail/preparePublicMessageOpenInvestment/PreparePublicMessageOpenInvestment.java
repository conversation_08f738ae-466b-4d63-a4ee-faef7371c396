package com.scbs.cis.model.response.subStateDetail.preparePublicMessageOpenInvestment;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PreparePublicMessageOpenInvestment {

    private String code;
    private String description;
    private String errorMessage;
    private String system;
    private String systemResultCode;
    private String systemResultDescription;
}
