package com.scbs.cis.model.response.preparePublicMessageOpenInvestment;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.scbs.cis.constants.StatusResponseEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PreparePublicMessageOpenInvestmentResponse {

    private StatusResponseEnum statusResponseEnum;
    private String data;

}
