package com.scbs.cis.model.response.cisCore.bankAccountUsage.get;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BankAccountUsageFromCisCoreResponse {

    @JsonProperty("bankAccountDetails")
    List<BankAccountUsageFromCisCore> bankAccountUsageFromCisCoreList;

}
