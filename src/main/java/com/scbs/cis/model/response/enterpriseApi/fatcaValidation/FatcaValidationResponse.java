package com.scbs.cis.model.response.enterpriseApi.fatcaValidation;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.scbs.cis.model.response.enterpriseApi.Status;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FatcaValidationResponse {

    private Status status;
    @JsonProperty("search_response_Details")
    private List<SearchResponseDetail> searchResponseDetails;

    @Data
    @NoArgsConstructor
    public static class SearchResponseDetail {

        @JsonProperty("FATCAstatus")
        private String fatcaStatus;
        @JsonProperty("custname")
        private String custName;
        @JsonProperty("idtype")
        private String idType;
        @JsonProperty("idnumber")
        private String idNumber;
        @JsonProperty("CustRm_No")
        private String custRmNo;
        @JsonProperty("CustomerType")
        private String customerType;
        @JsonProperty("Title")
        private String title;
        @JsonProperty("CityOfBirth")
        private String cityOfBirth;
        @JsonProperty("CountryOfBirth")
        private String countryOfBirth;
        @JsonProperty("CRS")
        private String crs;
        @JsonProperty("CountryTaxs")
        private List<CountryTax> countryTaxes;

    }

    @Data
    @NoArgsConstructor
    public static class CountryTax {

        @JsonProperty("CountryCode")
        private String countryCode;
        @JsonProperty("ForeignTIN")
        private String foreignTin;
        @JsonProperty("Reason")
        private String reason;
        @JsonProperty("Remark")
        private String remark;

    }

}
