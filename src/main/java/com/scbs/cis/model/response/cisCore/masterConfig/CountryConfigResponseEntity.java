package com.scbs.cis.model.response.cisCore.masterConfig;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.scbs.cis.model.StatusModel;
import lombok.Data;

@Data
@JsonIgnoreProperties
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CountryConfigResponseEntity {

    private StatusModel status;

    @JsonProperty("data")
    private CountryConfigResponse countryConfigResponse;

}
