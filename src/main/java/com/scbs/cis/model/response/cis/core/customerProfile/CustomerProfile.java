package com.scbs.cis.model.response.cis.core.customerProfile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.BigDecimalDeserializer;
import com.scbs.cis.deserializer.LocalDateTimeDeserializer;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerProfile {

    private String customerId;
    private String parentCustomerId;
    private String customerType;
    private String titleId;
    private String titleOtherTh;
    private String titleOtherEn;
    private String firstNameTh;
    private String middleNameTh;
    private String lastNameTh;
    private String firstNameEn;
    private String middleNameEn;
    private String lastNameEn;
    private String shortNameTh;
    private String shortNameEn;
    private LocalDate birthDate;
    private String gender;
    private String nationality;
    private String nationalityOther;
    private String maritalStatus;
    private String mobileNo;
    private String mobileCountry;
    private String mobileNoReserve;
    private String mobileReserveCountry;
    private String telephoneNo;
    private String officePhone;
    private String contactFax;
    private String officeFax;
    private String primaryEmail;
    private String reserveEmail;
    private String mailingChannel;
    private String mailingPostChannel;
    private String taxId;
    private String occupationId;
    private String occupationOther;
    private String businessTypeId;
    private String businessTypeOther;
    private String workPosition;
    private String companyNameTh;
    private String companyNameEn;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal income;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal incomeOther;
    private String sourceOfIncome;
    private String sourceOfIncomeOther;
    private String countrySourceOfIncome;
    private Boolean investmentKnowledgeFlag;
    private String investmentPurposeId;
    private String investmentPurposeOther;
    private String investorClass;
    private String qualifiedInvestorFlag;
    private Boolean politicalPersonFlag;
    private String politicalPosition;
    private Boolean isHavePoliticalRelationship;
    private String custStatus;
    private String createdBy;
    private LocalDate custCreateDate;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createdDatetime;
    private String updatedBy;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime updatedDatetime;
    private LocalDate custCloseDate;
    private String documentLanguage;
    private String specialCondition;
}
