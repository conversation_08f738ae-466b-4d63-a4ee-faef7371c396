package com.scbs.cis.model.response.cisCore.investmentAccoutDetail.getInvestmentAccountDetail;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InvestmentAccountDetailCisCoreResponse {

    private String customerId;
    private String custStatus;
    @JsonProperty("cusIdList")
    private List<CustomerDetailModel> cusIdList;

}
