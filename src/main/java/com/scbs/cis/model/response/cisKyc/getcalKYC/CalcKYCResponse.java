package com.scbs.cis.model.response.cisKyc.getcalKYC;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.LocalDateTimeDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CalcKYCResponse {
    private String referenceId;
    private String customerId;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime dataDateTime;
    private String newKycLevel;
    private String newKycReason;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime kycNextExpireDate;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime calculationResultDateTime;
}
