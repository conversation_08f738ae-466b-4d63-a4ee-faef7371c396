package com.scbs.cis.model.response.cisCore.customerProfile.createCustomertProfile;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.scbs.cis.model.response.cisCore.BaseCisCoreResponseEntity;
import lombok.Data;


@Data
@JsonIgnoreProperties
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CreateCustomerProfileFromCisCoreResponseEntity extends BaseCisCoreResponseEntity<CreateCustomerProfileFromCisCoreResponse> {


}


