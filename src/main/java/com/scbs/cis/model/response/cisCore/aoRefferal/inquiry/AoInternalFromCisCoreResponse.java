package com.scbs.cis.model.response.cisCore.aoRefferal.inquiry;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AoInternalFromCisCoreResponse {

    @JsonProperty("aoList")
    List<AoInternalFromCisCore> aoInternalFromCisCoreList;


}
