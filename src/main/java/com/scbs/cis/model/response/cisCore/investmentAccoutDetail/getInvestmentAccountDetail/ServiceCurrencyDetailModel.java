package com.scbs.cis.model.response.cisCore.investmentAccoutDetail.getInvestmentAccountDetail;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ServiceCurrencyDetailModel {
    private String serviceSubAccount;
    private String currency;
    private String serviceSubAccountStatus;
}
