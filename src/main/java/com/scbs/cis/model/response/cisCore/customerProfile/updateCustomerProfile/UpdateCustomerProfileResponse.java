package com.scbs.cis.model.response.cisCore.customerProfile.updateCustomerProfile;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdateCustomerProfileResponse {
    private String code;
    private String header;
    private String description;
}