package com.scbs.cis.model.response.cisCore.customerAo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.scbs.cis.model.StatusModel;
import com.scbs.cis.model.response.cisCore.customerAddress.getCustomerAddress.CustomerAddressFromCisCoreResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerAoFromCisCoreResponseEntity {

    private StatusModel status;

    @JsonProperty("data")
    private CustomerAoFromCisCoreResponse customerAoFromCisCoreResponse;

}