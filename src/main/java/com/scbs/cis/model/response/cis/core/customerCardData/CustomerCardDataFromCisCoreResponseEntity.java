package com.scbs.cis.model.response.cis.core.customerCardData;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.scbs.cis.model.StatusModel;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerCardDataFromCisCoreResponseEntity {
    private StatusModel status;

    @JsonProperty("data")
    private CustomerCardDataResponse customerCardDataResponse;

}
