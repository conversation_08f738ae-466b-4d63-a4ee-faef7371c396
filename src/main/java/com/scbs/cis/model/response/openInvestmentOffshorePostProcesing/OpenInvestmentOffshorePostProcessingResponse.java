package com.scbs.cis.model.response.openInvestmentOffshorePostProcesing;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.scbs.cis.constants.StatusResponseEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OpenInvestmentOffshorePostProcessingResponse {

    private StatusResponseEnum statusResponseEnum;
    private String data;

}
