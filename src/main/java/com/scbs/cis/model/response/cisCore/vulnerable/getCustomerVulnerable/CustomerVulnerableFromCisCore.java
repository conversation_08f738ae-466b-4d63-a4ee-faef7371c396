package com.scbs.cis.model.response.cisCore.vulnerable.getCustomerVulnerable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.scbs.cis.deserializer.BirthDateDeserializer;
import com.scbs.cis.deserializer.BooleanDeserializer;
import com.scbs.cis.deserializer.LocalDateDeserializer;
import com.scbs.cis.deserializer.StringDeserializer;
import com.scbs.cis.serializer.LocalDateTimeSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerVulnerableFromCisCore {

    private String customerVulnerableId;
    private String customerId ;
    @JsonDeserialize(using = BirthDateDeserializer.class)
    private LocalDate birthDate;
    private String age;
    private Boolean isVulByAge;
    private Boolean isVulByInvestmentKnowledge;
    private Boolean isVulByDisability;
    private Boolean isDisabledVul;
    private String remark;
    @JsonProperty("records")
    private List<RecordCustomerVulnerable> recordCustomerVulnerableList;
    private String createdBy;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createdDatetime;
    private String updatedBy ;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime updatedDatetime;
    @JsonDeserialize(using = StringDeserializer.class)
    private String migratedSource;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime migratedDatetime;
}
