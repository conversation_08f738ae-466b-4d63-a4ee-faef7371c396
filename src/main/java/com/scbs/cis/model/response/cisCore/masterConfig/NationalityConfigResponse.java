package com.scbs.cis.model.response.cisCore.masterConfig;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NationalityConfigResponse {

    private List<NationalityConfig> nationList;
    private int totalRow;
}
