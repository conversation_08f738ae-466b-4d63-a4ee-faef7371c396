package com.scbs.cis.model.response.cis.core.customerCardData;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.LocalDateTimeDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerCardData {
    private Integer runningId;
    private String customerId;
    private String cardType;
    private String cardNumber;
    private String cardIssuingCountry;
    private LocalDate cardIssueDate;
    private LocalDate cardExpiryDate;
    private Boolean currentInd;
    private LocalDate effectiveDate;
    private LocalDate endDate;
    private String createdBy;
    private String updatedBy;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime updatedDatetime;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createdDatetime;
}