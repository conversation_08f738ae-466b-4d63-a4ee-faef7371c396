package com.scbs.cis.model.response.cdb.inquiryCustomer;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerCreditLimit {

    private BigDecimal cardNoLimit ;
    private BigDecimal equityLimit;
    private BigDecimal tfexLimit;
    private BigDecimal sblLimit;
    private BigDecimal otcLimit;
    private BigDecimal bondLimit;
    private BigDecimal structureProductLimit;
    private BigDecimal offshoreLimit;

}
