package com.scbs.cis.model.response.createCustomerAccount;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreateCustomerAccountResponse {

    private String referenceId;
    private String applicationId;
    private String customerId;
    private String cancelReasonType;

}
