package com.scbs.cis.model.response.cisCore.customerCardData.getCustomerCardData;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.BirthDateDeserializer;
import com.scbs.cis.deserializer.LocalDateDeserializer;
import com.scbs.cis.deserializer.LocalDateTimeDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CustomerCardFromCisCore {

    private Integer runningId;
    private String customerId;
    private String cardType;
    private String cardNumber;
    private String cardIssuingCountry;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate cardIssueDate;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate cardExpiryDate;
    private Boolean currentInd;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate effectiveDate;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate endDate;
    private String createdBy;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createdDatetime;
    private String updatedBy;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime updatedDatetime;
    private String migratedSource;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime migratedDatetime;

}
