package com.scbs.cis.model.response.cisCore.customerOnboaringProfile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scbs.cis.deserializer.LocalDateDeserializer;
import com.scbs.cis.deserializer.LocalDateTimeDeserializer;
import com.scbs.cis.serializer.LocalDateTimeSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerOnboardingProfileFromCisCore {

    private String cisUid ;
    private String customerId;
    private String openChannel;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate custCreatedDate;
    private String verificationEntity;
    private String verificationRequestId;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime verificationDatetime;
    private String ialLevel;
    private String idpName;
    private String asName;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime dopaResultDatetime;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime nfcResultDatetime;
    private String createdBy;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createdDatetime;
    private String updatedBy ;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime updatedDatetime;
    private String migratedSource;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime migratedDatetime;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime idCreateDate;

}
