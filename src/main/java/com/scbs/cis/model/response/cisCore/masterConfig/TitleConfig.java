package com.scbs.cis.model.response.cisCore.masterConfig;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TitleConfig {
    private Integer id;
    private String code;
    private String descriptionTh;
    private String descriptionEn;
    private String codeSBA;
    private String codeBSB;
    private String codeFCN;
}
