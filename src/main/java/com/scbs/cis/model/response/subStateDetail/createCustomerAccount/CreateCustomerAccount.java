package com.scbs.cis.model.response.subStateDetail.createCustomerAccount;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreateCustomerAccount {

    private String code;
    private String description;
    private String system;
    private String errorMessage;
    private String systemResultCode;
    private String systemResultDescription;
    @JsonProperty("data")
    private Customer customer;
}
