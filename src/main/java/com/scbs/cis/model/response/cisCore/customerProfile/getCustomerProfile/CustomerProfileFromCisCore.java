package com.scbs.cis.model.response.cisCore.customerProfile.getCustomerProfile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scbs.cis.deserializer.BooleanDeserializer;
import com.scbs.cis.deserializer.LocalDateDeserializer;
import com.scbs.cis.deserializer.LocalDateTimeDeserializer;
import com.scbs.cis.serializer.LocalDateSerializer;
import com.scbs.cis.serializer.LocalDateTimeSerializer;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerProfileFromCisCore {

    private String customerId;
    private String parentCustomerId;
    private String customerType;
    private String titleId;
    private String titleOtherTh;
    private String titleOtherEn;
    private String firstNameTh;
    private String middleNameTh;
    private String lastNameTh;
    private String firstNameEn;
    private String middleNameEn;
    private String lastNameEn;
    private String shortNameTh;
    private String shortNameEn;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate birthDate;
    private String gender;
    private String nationality;
    private String nationalityOther;
    private String maritalStatus;
    private String mobileNo;
    private String mobileCountry;
    private String mobileNoReserve;
    private String mobileReserveCountry;
    private String telephoneNo;
    private String officePhone;
    private String contactFax;
    private String officeFax;
    private String primaryEmail;
    private String reserveEmail;
    private String mailingChannel;
    private String mailingPostChannel;
    private String taxId;
    private String occupationId;
    private String occupationOther;
    private String businessTypeId;
    private String businessTypeOther;
    private String workPosition;
    private String companyNameTh;
    private String companyNameEn;
    private BigDecimal income;
    private BigDecimal incomeOther;
    private String sourceOfIncome;
    private String sourceOfIncomeOther;
    private String countrySourceOfIncome;
    private Boolean investmentKnowledgeFlag;
    private String investmentPurposeId;
    private String investmentPurposeOther;
    private String investorClass;
    private String qualifiedInvestorFlag;
    private Boolean politicalPersonFlag;
    private String politicalPosition;
    private Boolean isHavePoliticalRelationship;
    private String custStatus;
    private String documentLanguage;
    private String specialCondition;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate custCreateDate;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate custCloseDate;
    private String createdBy;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createdDatetime;
    private String updatedBy;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime updatedDatetime;
    private String migratedSource;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime migratedDatetime;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime singleAppDatetime;
    @JsonDeserialize(using = BooleanDeserializer.class)
    private Boolean singleAppFlag;
}
