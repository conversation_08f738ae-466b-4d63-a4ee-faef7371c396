package com.scbs.cis.model.request.preparePublicMessageUpdateCustomerProfile;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scbs.cis.deserializer.LocalDateTimeDeserializer;
import com.scbs.cis.model.request.preparePublicMessageUpdateCustomerProfile.investmentProduct.InvestmentProduct;
import com.scbs.cis.serializer.LocalDateTimeSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PreparePublicMessageUpdateCustomerProfileRequest {

    private String referenceId;
    private String customerId;
    private CustomerProfile customerProfile;
    private List<CustomerRelationship> customerRelationship;
    private List<CustomerAo> customerAo;
    @JsonProperty("investmentProductList")
    private List<InvestmentProduct> investmentProductList; // TODO : ask sa about construct this
    @JsonProperty("investmentServiceList")
    private List<InvestmentService> investmentServiceList; // TODO : ask sa about construct this
    private Boolean isNewCustomer;
    private String evenTrigger;
    private String channelType;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime dataDateTime;

}
