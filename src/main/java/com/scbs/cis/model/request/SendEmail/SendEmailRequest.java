package com.scbs.cis.model.request.SendEmail;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scbs.cis.deserializer.LocalDateDeserializer;
import com.scbs.cis.serializer.LocalDateSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SendEmailRequest {

    private String referenceId;
    private String taskId;
    private String taskStateId;
    private String applicationId;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate applicationDate;
    private String customerId;
    private String cisUid;
    private String partner;
    private Boolean onboardingFlag;

    @JsonProperty("customerDetail")
    private CustomerDetailRequest customerDetailRequest;

    @JsonProperty("openInvestmentAccount")
    private OpenInvestmentAccountRequest openInvestmentAccountRequest;

}

