package com.scbs.cis.model.request.updateCustomerProfile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.LocalDateAllowTimeDeserializer;
import com.scbs.cis.deserializer.LocalDateDeserializer;
import com.scbs.cis.deserializer.StringDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateCustomerProfileRequest {

    @NotEmpty(message = "UpdateCustomerProfileRequest.referenceId must not be Empty")
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 36,message = "UpdateCustomerProfileRequest.referenceId must not > 36")
    private String referenceId;

    @NotEmpty(message = "UpdateCustomerProfileRequest.applicationId must not be Empty")
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 50,message = "UpdateCustomerProfileRequest.applicationId must not > 50")
    private String applicationId;

    @NotNull(message = "UpdateCustomerProfileRequest.applicationDate must not be Empty")
    @JsonDeserialize(using = LocalDateAllowTimeDeserializer.class)
    private LocalDate applicationDate;

    @NotEmpty(message = "UpdateCustomerProfileRequest.customerId must not be Empty")
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 12,message = "UpdateCustomerProfileRequest.customerId must not be Empty")
    private String customerId;

    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 15 ,message = "UpdateCustomerProfileRequest.cisUid must not > 15")
    private String cisUid;

    @NotEmpty(message = "UpdateCustomerProfileRequest.partner must not be Empty")
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 20,message = "UpdateCustomerProfileRequest.partner must not > 20")
    private String partner;

    @Valid
    @JsonProperty("customerProfile")
    private CustomerProfileRequest customerProfileRequest;

    @Valid
    @JsonProperty("customerCard")
    private CustomerCardRequest customerCardRequest;

    @Valid
    @JsonProperty("relationship")
    private List<RelationshipsRequest> relationshipRequest;

    @Valid
    @JsonProperty("address")
    private List<AddressRequest> addressRequestList;

    @Valid
    @JsonProperty("vulnerable")
    private VulnerableRequest vulnerableRequest;

    @Valid
    @JsonProperty("profileCisUid")
    private ProfileCisUidRequest profileCisUidRequest;

    @Valid
    @JsonProperty("addressCisUid")
    private AddressCisUidRequest addressCisUidRequest;

    @Valid
    @JsonProperty("onboardingProfiles")
    private OnboardingProfilesRequest onboardingProfileRequest;

    @Valid
    @JsonProperty("suitability")
    private SuitabilityRequest suitabilityRequest;

    @Valid
    @JsonProperty("customerKyc")
    private CustomerKycRequest customerKycRequest;

    @Valid
    @JsonProperty("customerFatca")
    private CustomerFatcaRequest customerFatcaRequest;

    @Valid
    @JsonProperty("customerAo")
    private List<CustomerAoRequest> customerAoRequestList;

    @Valid
    @JsonProperty("referralInfo")
    private ReferralInfoRequest referralInfoRequest;
}
