package com.scbs.cis.model.request.createCisuidAccount;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BankAccountUsageRequest {

    private String bankAccountNumber;
    private String bankCode;
    private String branchCode;
    private String bankAccountType;
    private String paymentMethod;
    private String accountPurpose;
    private Boolean isMainAccount;
    private String remark;

}
