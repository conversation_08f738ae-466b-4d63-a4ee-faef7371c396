package com.scbs.cis.model.request.preparePublicMessageUpdateCustomerProfile.updateCustomerProfileToCisCore;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scbs.cis.deserializer.LocalDateDeserializer;
import com.scbs.cis.serializer.LocalDateSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BankAccountUsageRequest {
    private String	bankAccountNumber;
    private String	branchCode;
    private String	bankCode;
    private String	bankAccountType;
    private String	paymentMethod;
    private String	accountPurpose;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate effectiveDate; // todo :  ask dateFormat
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate	endDate;
    private Boolean	isMainAccount;
    private String	remark;
}
