package com.scbs.cis.model.request.openInvestmentServiceGtnDone;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class Product {

    private String productType;
    private String accountType;
    private String tradingSystem;
    private String accountNumber;
    private String customerCode;
    @JsonProperty("serviceList")
    private List<ServiceGtnDone> serviceGtnDone;
    @JsonProperty("bankAccountUsage")
    private List<BankAccountUsage> bankAccountUsageRequestList;
    @JsonProperty("investmentLimitAcct")
    private InvestmentLimitAcctRequest investmentLimitAcctRequest;

}
