package com.scbs.cis.model.request.publicMessage.openInvestmentAccount;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scbs.cis.deserializer.BooleanDeserializer;
import com.scbs.cis.deserializer.LocalDateDeserializer;
import com.scbs.cis.deserializer.StringDeserializer;
import com.scbs.cis.serializer.LocalDateSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Size;
import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BankAccountUsage {

    @Size(max = 20,message = "openInvestmentAccount.productList[].bankAccountUsage[].bankAccountNumber must not > 20")
    @JsonDeserialize(using = StringDeserializer.class)
    private String bankAccountNumber;
    @Size(max = 3,message = "openInvestmentAccount.productList[].bankAccountUsage[].bankCode must not > 3")
    @JsonDeserialize(using = StringDeserializer.class)
    private String bankCode;
    @Size(max = 5,message = "openInvestmentAccount.productList[].bankAccountUsage[].branchCode must not > 5")
    @JsonDeserialize(using = StringDeserializer.class)
    private String branchCode;
    @Size(max = 10,message = "openInvestmentAccount.productList[].bankAccountUsage[].bankAccountType must not > 10")
    @JsonDeserialize(using = StringDeserializer.class)
    private String bankAccountType;
    @Size(max = 10,message = "openInvestmentAccount.productList[].bankAccountUsage[].paymentMethod must not > 10")
    @JsonDeserialize(using = StringDeserializer.class)
    private String paymentMethod;
    @Size(max = 10,message = "openInvestmentAccount.productList[].bankAccountUsage[].accountPurpose must not > 10")
    @JsonDeserialize(using = StringDeserializer.class)
    private String accountPurpose;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate effectiveDate;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate endDate;
    @JsonDeserialize(using = StringDeserializer.class)
    private String remark;

    @JsonDeserialize(using = BooleanDeserializer.class)
    private Boolean isMainAccount;
}