package com.scbs.cis.model.request.publicMessage;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.BigDecimalDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerCreditLimit {

    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal cardNoLimit;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal equityLimit;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal tfexLimit;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal sblLimit;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal otcLimit;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal bondLimit;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal structureProductLimit;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal offshoreLimit;

}