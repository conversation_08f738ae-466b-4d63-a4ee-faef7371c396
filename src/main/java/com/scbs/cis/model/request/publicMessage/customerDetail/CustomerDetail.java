package com.scbs.cis.model.request.publicMessage.customerDetail;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerDetail {
    @Valid
    CustomerProfile customerProfile;
    @Valid
    CustomerCard customerCard;
    @Valid
    List<Relationships> relationship;
    @Valid
    @NotNull
    List<Address> address;
    @Valid
    Suitability suitability;
    @Valid
    CustomerKyc kyc;
    @Valid
    Vulnerable vulnerable;
    @Valid
    ReferralInfo referralInfo;
    @Valid
    OnboardingProfiles onboardingProfiles;
    @Valid
    List<AccountDetail> accountDetails;
}