package com.scbs.cis.model.request.task;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.StringDeserializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data

public class CustomerIdRequestModel {
    @JsonProperty("customerId")
    @JsonDeserialize(using = StringDeserializer.class)
    private String customerId;

}
