package com.scbs.cis.model.request.cisCore.customerProfile.post.UpdateCustomerProfileFromCisCoreModel;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReferralInfo {
    private String referralType;
    private String referralCode;
    private String referralBranchCode;
    private String referralStaffName;
    private String referrerCustomerCard;
    private String referrerCustomerCardType;
}