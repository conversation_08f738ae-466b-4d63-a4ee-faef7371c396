package com.scbs.cis.model.request.preparePublicMessageUpdateCustomerProfile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateCustomerProfileToSbaDone {

    private String referenceId;
    private String taskId;
    private String taskStateId;
    private String applicationId;
    private String partner;
    private String customerId;
    private String cisUid;
}
