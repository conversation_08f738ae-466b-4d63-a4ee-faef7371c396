package com.scbs.cis.model.request.onboardingUpdateCustomerProfile.updateCustomerProfileToCisCore;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scbs.cis.deserializer.IntegerDeserializer;
import com.scbs.cis.deserializer.LocalDateDeserializer;
import com.scbs.cis.deserializer.StringDeserializer;
import com.scbs.cis.serializer.LocalDateSerializer;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerFatcaRequest {

    @JsonDeserialize(using = StringDeserializer.class)
    private String cityCodeOfBirth;
    @JsonDeserialize(using = StringDeserializer.class)
    private String cityOfBirth;
    @JsonDeserialize(using = StringDeserializer.class)
    private String countryOfBirth;
    @JsonDeserialize(using = StringDeserializer.class)
    private String fatcaVersion;
    @JsonDeserialize(using = StringDeserializer.class)
    private String ans01;
    @JsonDeserialize(using = StringDeserializer.class)
    private String ans02;
    @JsonDeserialize(using = StringDeserializer.class)
    private String ans03;
    @JsonDeserialize(using = StringDeserializer.class)
    private String ans04;
    @JsonDeserialize(using = StringDeserializer.class)
    private String ans05;
    @JsonDeserialize(using = StringDeserializer.class)
    private String ans06;
    @JsonDeserialize(using = StringDeserializer.class)
    private String ans07;
    @JsonDeserialize(using = StringDeserializer.class)
    private String ans08;
    @JsonDeserialize(using = StringDeserializer.class)
    private String ans09;
    @JsonDeserialize(using = StringDeserializer.class)
    private String ans10;
    private List<CustomerCrs> customerCrsList;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate crsDeclarationDate;

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomerCrs {

        @JsonDeserialize(using = IntegerDeserializer.class)
        private int recNo;
        @JsonDeserialize(using = StringDeserializer.class)
        private String countryTax;
        @JsonDeserialize(using = StringDeserializer.class)
        private String tin;
        @JsonDeserialize(using = StringDeserializer.class)
        private String reasonCode;
        @JsonDeserialize(using = StringDeserializer.class)
        private String reasonMsg;

    }
}
