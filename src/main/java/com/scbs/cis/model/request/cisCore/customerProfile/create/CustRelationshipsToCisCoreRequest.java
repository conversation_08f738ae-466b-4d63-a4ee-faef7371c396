package com.scbs.cis.model.request.cisCore.customerProfile.create;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scbs.cis.serializer.LocalDateSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor

@JsonIgnoreProperties(ignoreUnknown = true)
public class CustRelationshipsToCisCoreRequest {

    private String relationshipType;
    private String relationshipId;
    private String relationshipOther;
    private String titleId;
    private String titleOtherTh;
    private String titleOtherEn;
    private String firstNameTh;
    private String middleNameTh;
    private String lastNameTh;
    private String firstNameEn;
    private String middleNameEn;
    private String lastNameEn;
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate birthDate;
    private String gender;
    private String nationality;
    private String nationalityOther;
    private String cardType;
    private String cardNumber;
    private String cardIssuingCountry;
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate cardIssueDate;
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate cardExpiryDate;
    private String mobileTelNo;
    private String telephoneNo;
    private String officeTelephoneNo;
    private String contactFaxNo;
    private String officeFaxNo;
    private String emailAddress;
    private String officeAddress;
    private String address1;
    private String address2;
    private String address3;
    private String address4;
    private String documentLanguage;
    private Boolean politicalPersonFlag;
    private String politicalPosition;


}
