package com.scbs.cis.model.request.cisCore.investmentAccount.insertUpdate;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@JsonIgnoreProperties
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ServiceRequest {

    private String serviceType;
    private String profileId;
    private String serviceStatus;
    private List<AccountRequest> accounts;
}
