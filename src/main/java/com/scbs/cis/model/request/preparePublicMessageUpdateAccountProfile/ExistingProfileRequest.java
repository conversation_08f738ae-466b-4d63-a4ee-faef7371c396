package com.scbs.cis.model.request.preparePublicMessageUpdateAccountProfile;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExistingProfileRequest {

    @JsonProperty("customerProfile")
    CustomerProfile customerProfile;
    @JsonProperty("customerRelationship")
    List<CustomerRelationship> customerRelationship;
    @JsonProperty("productList")
    List<Product> productList;

}
