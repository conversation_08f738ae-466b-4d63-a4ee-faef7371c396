package com.scbs.cis.model.request.createCustomerAccount;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CreateCustomerRequest {

    @Valid
    @NotNull
    @JsonProperty("custProfile")
    private CustomerProfileRequest custProfileRequest;
    @Valid
    @JsonProperty("addresses")
    private List<AddressRequest> addressesRequestList;
    @Valid
    @JsonProperty("custCard")
    private CustomerCardRequest custCardRequest;
    @Valid
    @JsonProperty("custRelationships")
    private List<RelationshipsRequest> custRelationshipsRequestList;
    @Valid
    @JsonProperty("custVulnerable")
    private VulnerableRequest customerVulnerableRequest;

}
