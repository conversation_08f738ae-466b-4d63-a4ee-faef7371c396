package com.scbs.cis.model.request.onboardingUpdateCustomerProfile;


import static com.scbs.cis.service.taskService.UpdateCustomerProfileValidateFieldConstant.FIELD_ACCOUNT_DETAILS;
import static com.scbs.cis.service.taskService.UpdateCustomerProfileValidateFieldConstant.FIELD_ADDRESS;
import static com.scbs.cis.service.taskService.UpdateCustomerProfileValidateFieldConstant.FIELD_ADDRESS_CIS_UID;
import static com.scbs.cis.service.taskService.UpdateCustomerProfileValidateFieldConstant.FIELD_CUSTOMER_AO;
import static com.scbs.cis.service.taskService.UpdateCustomerProfileValidateFieldConstant.FIELD_CUSTOMER_CARD;
import static com.scbs.cis.service.taskService.UpdateCustomerProfileValidateFieldConstant.FIELD_CUSTOMER_FATCA;
import static com.scbs.cis.service.taskService.UpdateCustomerProfileValidateFieldConstant.FIELD_CUSTOMER_KYC;
import static com.scbs.cis.service.taskService.UpdateCustomerProfileValidateFieldConstant.FIELD_CUSTOMER_PROFILE;
import static com.scbs.cis.service.taskService.UpdateCustomerProfileValidateFieldConstant.FIELD_ONBOARDING_PROFILES;
import static com.scbs.cis.service.taskService.UpdateCustomerProfileValidateFieldConstant.FIELD_PROFILE_CIS_UID;
import static com.scbs.cis.service.taskService.UpdateCustomerProfileValidateFieldConstant.FIELD_REFERRAL_INFO;
import static com.scbs.cis.service.taskService.UpdateCustomerProfileValidateFieldConstant.FIELD_RELATIONSHIP;
import static com.scbs.cis.service.taskService.UpdateCustomerProfileValidateFieldConstant.FIELD_SUITABILITY;
import static com.scbs.cis.service.taskService.UpdateCustomerProfileValidateFieldConstant.FIELD_VULNERABLE;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scbs.cis.deserializer.LocalDateAllowTimeDeserializer;
import com.scbs.cis.deserializer.StringDeserializer;
import com.scbs.cis.model.request.onboardingUpdateCustomerProfile.updateCustomerProfileToCisCore.AccountDetailRequest;
import com.scbs.cis.model.request.onboardingUpdateCustomerProfile.updateCustomerProfileToCisCore.AddressCisUidRequest;
import com.scbs.cis.model.request.onboardingUpdateCustomerProfile.updateCustomerProfileToCisCore.AddressRequest;
import com.scbs.cis.model.request.onboardingUpdateCustomerProfile.updateCustomerProfileToCisCore.CustomerAoRequest;
import com.scbs.cis.model.request.onboardingUpdateCustomerProfile.updateCustomerProfileToCisCore.CustomerCardRequest;
import com.scbs.cis.model.request.onboardingUpdateCustomerProfile.updateCustomerProfileToCisCore.CustomerFatcaRequest;
import com.scbs.cis.model.request.onboardingUpdateCustomerProfile.updateCustomerProfileToCisCore.CustomerKycRequest;
import com.scbs.cis.model.request.onboardingUpdateCustomerProfile.updateCustomerProfileToCisCore.CustomerProfileRequest;
import com.scbs.cis.model.request.onboardingUpdateCustomerProfile.updateCustomerProfileToCisCore.OnboardingProfileRequest;
import com.scbs.cis.model.request.onboardingUpdateCustomerProfile.updateCustomerProfileToCisCore.ProfileCisUidRequest;
import com.scbs.cis.model.request.onboardingUpdateCustomerProfile.updateCustomerProfileToCisCore.ReferralInfoRequest;
import com.scbs.cis.model.request.onboardingUpdateCustomerProfile.updateCustomerProfileToCisCore.RelationshipRequest;
import com.scbs.cis.model.request.onboardingUpdateCustomerProfile.updateCustomerProfileToCisCore.SuitabilityRequest;
import com.scbs.cis.model.request.onboardingUpdateCustomerProfile.updateCustomerProfileToCisCore.VulnerableRequest;
import com.scbs.cis.serializer.LocalDateSerializer;
import java.time.LocalDate;
import java.util.List;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.USE_DEFAULTS)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateCustomerProfileRequest {

    @NotEmpty(message = "createInvestmentAccountRequest.referenceId must not be Empty")
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 36, message = "createInvestmentAccountRequest.referenceId must not > 36")
    private String referenceId;
    @NotEmpty(message = "createInvestmentAccountRequest.applicationId must not be Empty")
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 50, message = "createInvestmentAccountRequest.applicationId must not > 50")
    private String applicationId;
    @NotNull(message = "createInvestmentAccountRequest.applicationDate must not be Empty")
    @JsonDeserialize(using = LocalDateAllowTimeDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate applicationDate;
    @NotEmpty(message = "createInvestmentAccountRequest.customerId must not be Empty")
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 12, message = "createInvestmentAccountRequest.customerId must not be Empty")
    private String customerId;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 15, message = "createInvestmentAccountRequest.cisUid must not > 15")
    private String cisUid;
    @NotEmpty(message = "createInvestmentAccountRequest.partner must not be Empty")
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 20, message = "createInvestmentAccountRequest.partner must not > 20")
    private String partner;
    @JsonProperty(FIELD_CUSTOMER_PROFILE)
    private CustomerProfileRequest customerProfileRequest;
    @JsonProperty(FIELD_CUSTOMER_CARD)
    private CustomerCardRequest customerCardRequest;
    @JsonProperty(FIELD_RELATIONSHIP)
    private List<RelationshipRequest> relationshipRequest;
    @JsonProperty(FIELD_ADDRESS)
    private List<AddressRequest> addressRequestList;
    @JsonProperty(FIELD_VULNERABLE)
    private VulnerableRequest vulnerableRequest;
    @JsonProperty(FIELD_PROFILE_CIS_UID)
    private ProfileCisUidRequest profileCisUidRequest;
    @JsonProperty(FIELD_ADDRESS_CIS_UID)
    private AddressCisUidRequest addressCisUidRequest;
    @JsonProperty(FIELD_ONBOARDING_PROFILES)
    private OnboardingProfileRequest onboardingProfileRequest;
    @JsonProperty(FIELD_SUITABILITY)
    private SuitabilityRequest suitabilityRequest;
    @JsonProperty(FIELD_CUSTOMER_KYC)
    private CustomerKycRequest customerKycRequest;
    @JsonProperty(FIELD_CUSTOMER_FATCA)
    private CustomerFatcaRequest customerFatcaRequest;
    @JsonProperty(FIELD_CUSTOMER_AO)
    private List<CustomerAoRequest> customerAoRequestList;
    @JsonProperty(FIELD_REFERRAL_INFO)
    private ReferralInfoRequest referralInfoRequest;
    @JsonProperty(FIELD_ACCOUNT_DETAILS)
    private List<AccountDetailRequest> accountDetailRequestList;

}
