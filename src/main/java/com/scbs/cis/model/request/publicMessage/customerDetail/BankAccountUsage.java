package com.scbs.cis.model.request.publicMessage.customerDetail;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scbs.cis.deserializer.BooleanDeserializer;
import com.scbs.cis.deserializer.LocalDateDeserializer;
import com.scbs.cis.deserializer.StringDeserializer;
import com.scbs.cis.serializer.LocalDateSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Size;
import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BankAccountUsage {
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 20,message = "bankAccountUsage[].bankAccountNumber must not > 20")
    private String bankAccountNumber;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 10,message = "bankAccountUsage[].bankAccountType must not > 10")
    private String bankAccountType;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 3,message = "bankAccountUsage[].bankCode must not > 3")
    private String bankCode;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 5,message = "bankAccountUsage[].branchCode must not > 5")
    private String branchCode;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 10,message = "bankAccountUsage[].paymentMethod must not > 10")
    private String paymentMethod;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 10,message = "bankAccountUsage[].accountPurpose must not > 10")
    private String accountPurpose;
    @JsonDeserialize(using = BooleanDeserializer.class)
    private Boolean isMainAccount;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate effectiveDate;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate endDate;
    @JsonDeserialize(using = StringDeserializer.class)
    private String remark;
}