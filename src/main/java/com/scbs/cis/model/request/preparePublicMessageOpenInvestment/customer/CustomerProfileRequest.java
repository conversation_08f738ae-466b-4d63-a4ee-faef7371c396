package com.scbs.cis.model.request.preparePublicMessageOpenInvestment.customer;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scbs.cis.deserializer.*;
import com.scbs.cis.serializer.LocalDateSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CustomerProfileRequest {

    private String customerType;
    private String titleId;
    private String titleOtherTh;
    private String firstNameTh;
    private String middleNameTh;
    private String lastNameTh;
    private String shortNameTh;
    private String titleOtherEn;
    private String firstNameEn;
    private String middleNameEn;
    private String lastNameEn;
    private String shortNameEn;
    @JsonDeserialize(using = BirthDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate birthDate;
    private String gender;
    private String nationality;
    private String nationalityOther;
    private String maritalStatus;
    private String mobileNo;
    private String mobileCountry;
    private String mobileNoReserve;
    private String mobileReserveCountry;
    private String telephoneNo;
    private String officePhone;
    private String contactFax;
    private String officeFax;
    private String primaryEmail;
    private String reserveEmail;
    private String mailingChannel;
    private String mailingPostChannel;
    private String taxId;
    private String occupationId;
    private String occupationOther;
    private String businessTypeId;
    private String businessTypeOther;
    private String workPosition;
    private String companyNameTh;
    private String companyNameEn;
    private BigDecimal income;
    private BigDecimal incomeOther;
    private String incomeLevel;
    private String sourceOfIncome;
    private String sourceOfIncomeOther;
    private String countrySourceOfIncome;
    private Boolean investmentKnowledgeFlag;
    private String investmentPurposeId;
    private String investmentPurposeOther;
    private String investorClass;
    private String qualifiedInvestorFlag;
    private Boolean politicalPersonFlag;
    private String politicalPosition;
    private Boolean isHavePoliticalRelationship;
    private BigDecimal aum;
    private String segment;
    private String segmentDescription;
    private String documentLanguage;
    private String customerMobileCountry;
    private String customerMobileNo;
    private String customerPrimaryEmail;
    private String customerReserveEmail;

}