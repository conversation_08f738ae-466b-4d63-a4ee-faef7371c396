package com.scbs.cis.model.request.cisCore.customerProfile.post.UpdateCustomerProfileFromCisCoreModel;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@JsonIgnoreProperties
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CustomerSuit {
    private String suit1;
    private String suit2;
    private String suit3;
    private String suit4;
    private String suit5;
    private String suit6;
    private String suit7;
    private String suit8;
    private String suit9;
    private String suit10;
    private String suit11;
    private String suit12;
    private String suit13;
    private String suit14;
    private String suit15;
    private String suit16;
    private String suit17;
    private String suit18;
    private Integer totalSuitScoreDs;
    private Integer totalSuitScoreDa;
    private Integer suitLevelDs;
    private Integer suitLevelDa;
    private LocalDate suitabilityEvaluationDate;
    private LocalDate suitabilityExpireDate;
    private Boolean tfexAcceptRiskFlag;
    private Boolean offshoreAcceptRiskFlag;
    private Integer snInvestmentCondition;
    private String suitFormVersion;
    private String source;
    private LocalDate nextReviewDate;
}