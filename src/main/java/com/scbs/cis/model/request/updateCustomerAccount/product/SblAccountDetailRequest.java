package com.scbs.cis.model.request.updateCustomerAccount.product;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.BigDecimalDeserializer;
import com.scbs.cis.deserializer.LocalDateDeserializer;
import com.scbs.cis.deserializer.StringDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SblAccountDetailRequest { // CM ?

//    @NotEmpty(message = "SblAccountDetailRequestPrepare.openBorrowingContract must not be Empty")
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 1,message = "SblAccountDetailRequestPrepare.openBorrowingContract must not > 1")
    private String openBorrowingContract;
//    @NotNull(message = "SblAccountDetailRequestPrepare.openDate must not be Empty")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate openDate;
//    @NotNull(message = "SblAccountDetailRequestPrepare.effectiveDate must not be Empty")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate effectiveDate;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate closeDate;
//    @NotEmpty(message = "SblAccountDetailRequestPrepare.enableBorrowing must not be Empty")
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 1,message = "SblAccountDetailRequestPrepare.enableBorrowing must not > 1")
    private String enableBorrowing;
//    @NotNull(message = "SblAccountDetailRequestPrepare.appCreditLine must not be Empty")
    @Digits(integer = 16, fraction = 2, message = "SblAccountDetailRequestPrepare.appCreditLine must be (16,2)")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal appCreditLine;
//    @NotEmpty(message = "SblAccountDetailRequestPrepare.shortSaleOverBorrow must not be Empty")
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 1,message = "SblAccountDetailRequestPrepare.shortSaleOverBorrow must not > 1")
    private String shortSaleOverBorrow;
}
