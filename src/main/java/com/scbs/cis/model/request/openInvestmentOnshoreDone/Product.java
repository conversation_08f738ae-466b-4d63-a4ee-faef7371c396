package com.scbs.cis.model.request.openInvestmentOnshoreDone;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Product {

    private String productType;
    private String accountType;
    private String tradingSystem;
    private String accountNumber;
    private String customerCode;
    @JsonProperty("serviceList")
    private List<ServiceOnshoreDone> serviceOnshoreDone;
    @JsonProperty("bankAccountUsage")
    private List<BankAccountUsage> bankAccountUsageList;
    @JsonProperty("investmentLimitAcct")
    private InvestmentLimitAcctRequest investmentLimitAcctRequest;

}
