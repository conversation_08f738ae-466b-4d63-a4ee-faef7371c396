package com.scbs.cis.model.request.publicMessage.customerDetail;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.StringDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AccountDetail {
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 15,message = "accountDetails.cisUid must not > 15")
    private String cisUid;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 50,message = "accountDetails.accountNumber must not > 50")
    private String accountNumber;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 20,message = "accountDetails.productType must not > 20")
    private String productType;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 3,message = "accountDetails.accountType must not > 3")
    private String accountType;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 20,message = "accountDetails.accountStatus must not > 20")
    private String accountStatus;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 20,message = "accountDetails.customerCode must not > 20")
    private String customerCode;
    private List<AccountServiceDetail> serviceList;
    @Valid
    private List<BankAccountUsage> bankAccountUsage;
    @Valid
    private InvestmentLimit investmentLimit;
}