package com.scbs.cis.model.request.publicMessage.customerDetail;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.BooleanDeserializer;
import com.scbs.cis.deserializer.StringDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Size;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class Vulnerable {

    @JsonDeserialize(using = BooleanDeserializer.class)
    private Boolean isVulByAge;
    @JsonDeserialize(using = BooleanDeserializer.class)
    private Boolean isVulByInvestmentKnowledge;
    @JsonDeserialize(using = BooleanDeserializer.class)
    private Boolean isDisableVul;
    @Size(max = 500,message = "vulnerable.remarl must not > 500")
    @JsonDeserialize(using = StringDeserializer.class)
    private String remark;
}