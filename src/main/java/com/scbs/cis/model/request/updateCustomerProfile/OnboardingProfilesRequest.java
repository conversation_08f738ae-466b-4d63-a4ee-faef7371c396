package com.scbs.cis.model.request.updateCustomerProfile;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.LocalDateTimeDeserializer;
import com.scbs.cis.deserializer.StringDeserializer;
import com.scbs.cis.deserializer.StringWithOutCommonFormatDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OnboardingProfilesRequest {

    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 10,message = "OnboardingProfiles.verificationEntity must not > 10")
    private String verificationEntity;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 64,message = "OnboardingProfiles.verificationRequestId must not > 64")
    private String verificationRequestId;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime verificationDatetime;
    @Size(max = 10,message = "OnboardingProfiles.ialLevel must not > 10")
    @JsonDeserialize(using = StringWithOutCommonFormatDeserializer.class)
    private String ialLevel;
    @Size(max = 100,message = "OnboardingProfiles.idpName must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String idpName;
    @Size(max = 100,message = "OnboardingProfiles.asName must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String asName;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime dopaResultDatetime;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime nfcResultDatetime;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime otpDatetime;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 100,message = "OnboardingProfiles.signerName must not >100")
    private String signerName;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 20,message = "OnboardingProfiles.branchCode must not > 20")
    private String branchCode;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 100,message = "OnboardingProfiles.officerName must not > 100")
    private String officerName;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 20,message = "OnboardingProfiles.officerId must not > 20")
    private String officerId;


}
