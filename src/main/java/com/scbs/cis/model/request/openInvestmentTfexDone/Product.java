package com.scbs.cis.model.request.openInvestmentTfexDone;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Product {

    private String productType;
    private String accountType;
    private String tradingSystem;
    private String accountNumber;
    private String customerCode;
    @JsonProperty("bankAccountUsage")
    private List<BankAccountUsage> bankAccountUsageList;
    @JsonProperty("investmentLimitAcct")
    private InvestmentLimitAcctRequest investmentLimitAcctRequest;

}
