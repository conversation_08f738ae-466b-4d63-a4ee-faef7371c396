package com.scbs.cis.model.request.openInvestmentDa;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.BooleanDeserializer;
import com.scbs.cis.deserializer.StringDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BankAccountUsage {

    @NotEmpty(message = "bankAccountNumber must not be Empty")
    @JsonDeserialize(using = StringDeserializer.class)
    private String bankAccountNumber;
    @NotEmpty(message = "bankCode must not be Empty")
    @JsonDeserialize(using = StringDeserializer.class)
    private String bankCode;
    @NotEmpty(message = "branchCode must not be Empty")
    @JsonDeserialize(using = StringDeserializer.class)
    private String branchCode;
    @JsonDeserialize(using = StringDeserializer.class)
    private String bankAccountType;
    @NotEmpty(message = "paymentMethod must not be Empty")
    @JsonDeserialize(using = StringDeserializer.class)
    private String paymentMethod;
    @NotEmpty(message = "accountPurpose must not be Empty")
    @JsonDeserialize(using = StringDeserializer.class)
    private String accountPurpose;
    @NotNull(message = "isMainAccount must not be Null")
    @JsonDeserialize(using = BooleanDeserializer.class)
    private Boolean isMainAccount; // TODO ASK SA this field not have in prepare O/M
    @JsonDeserialize(using = StringDeserializer.class)
    private String remark;

}