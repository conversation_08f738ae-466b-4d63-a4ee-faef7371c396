package com.scbs.cis.model.request.openInvestmentDa;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.StringDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.Valid;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class Product {
    @JsonDeserialize(using = StringDeserializer.class)
    private String accountNumber;
    @JsonDeserialize(using = StringDeserializer.class)
    private String productType;
    @JsonDeserialize(using = StringDeserializer.class)
    private String accountType;
    @Valid
    @JsonProperty("investmentLimitAcct")
    private InvestmentLimitAcct investmentLimitAcct;
}