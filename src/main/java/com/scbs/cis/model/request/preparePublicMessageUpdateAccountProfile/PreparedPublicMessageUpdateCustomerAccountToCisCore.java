package com.scbs.cis.model.request.preparePublicMessageUpdateAccountProfile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PreparedPublicMessageUpdateCustomerAccountToCisCore {
    private String taskId;
    private String taskStateId;
    private String applicationId;
    private String referenceId;
    private String customerId;
    private String cisUid;
    private String partner;
    @JsonProperty("existingProfile")
    private ExistingProfileRequest existingProfileRequest;

    @JsonProperty("toBeProfile")
    private ToBeProfileRequest toBeProfileRequest;
}