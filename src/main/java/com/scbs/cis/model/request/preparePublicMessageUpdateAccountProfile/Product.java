package com.scbs.cis.model.request.preparePublicMessageUpdateAccountProfile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)

public class Product {

    private String  accountNumber;
    private String  customerCode;
    private String  productType;
    private String  accountType;
    private String  accountStatus;
    @JsonProperty("serviceList")
    private List<Service> serviceList;
    @JsonProperty("bankAccountUsage")
    List<BankAccountUsage> bankAccountUsageList;
    @JsonProperty("investmentLimitAcct")
    private InvestmentLimitAcct investmentLimitAcct;

}
