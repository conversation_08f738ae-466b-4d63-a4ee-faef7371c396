package com.scbs.cis.model.request.SendEmail;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SuitabilityRequest {

    private Integer totalSuitScoreDs;
    private Integer totalSuitScoreDa;
    private Integer suitLevelDs;
    private Integer suitLevelDa;
    private Boolean tfexAcceptRiskFlag;
    private Boolean offshoreAcceptRiskFlag;
    private Integer snInvestmentCondition;
}
