package com.scbs.cis.model.request.preparePublicMessageOpenInvestment.customerCreditLimit;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor

public class CustomerCreditLimitRequest {

    private BigDecimal cardNoLimit;
    private BigDecimal equityLimit;
    private BigDecimal tfexLimit;
    private BigDecimal sblLimit;
    private BigDecimal otcLimit;
    private BigDecimal bondLimit;
    private BigDecimal structureProductLimit;
    private BigDecimal offshoreLimit;

}
