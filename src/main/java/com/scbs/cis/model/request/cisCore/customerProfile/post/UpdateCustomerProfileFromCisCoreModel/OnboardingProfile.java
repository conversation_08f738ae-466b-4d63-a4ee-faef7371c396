package com.scbs.cis.model.request.cisCore.customerProfile.post.UpdateCustomerProfileFromCisCoreModel;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OnboardingProfile {
    private String openChannel;
    private LocalDateTime custCreatedDate;
    private String verificationEntity;
    private String ialLevel;
    private String verificationRequestId;
    private String idpName;
    private String asName;
    private LocalDateTime verificationDatetime;
    private LocalDateTime dopaResultDatetime;
    private LocalDateTime nfcResultDatetime;
}