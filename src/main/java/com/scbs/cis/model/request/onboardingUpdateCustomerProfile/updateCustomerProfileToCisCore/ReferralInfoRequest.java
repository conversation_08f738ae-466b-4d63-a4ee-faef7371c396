package com.scbs.cis.model.request.onboardingUpdateCustomerProfile.updateCustomerProfileToCisCore;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReferralInfoRequest {
    private String	referralType;
    private String	referralCode;
    private String	referralBranchCode;
    private String	referralStaffName;
    private String	referrerCustomerCard;
    private String  referrerCustomerCardType;
}
