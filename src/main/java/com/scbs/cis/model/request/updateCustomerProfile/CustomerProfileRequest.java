package com.scbs.cis.model.request.updateCustomerProfile;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;

import static com.scbs.cis.constants.CisConstants.REG_EX_NUMERIC;
import static com.scbs.cis.constants.CisConstants.REG_EX_NUMERIC_13_DIGIT;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerProfileRequest {

    @Size(max = 20, message = "customerProfile.customerType must not > 20")
    @JsonDeserialize(using = StringDeserializer.class)
    private String customerType;
    @Size(max = 3, message = "customerProfile.titleId must not > 3")
    @JsonDeserialize(using = StringDeserializer.class)
    private String titleId;
    @Size(max = 50, message = "customerProfile.titleOtherTh must not > 50")
    @JsonDeserialize(using = StringDeserializer.class)
    private String titleOtherTh;
    @Size(max = 100, message = "customerProfile.firstNameTh must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String firstNameTh;
    @Size(max = 100, message = "customerProfile.middleNameTh must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String middleNameTh;
    @Size(max = 100, message = "customerProfile.lastNameTh must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String lastNameTh;
    @Size(max = 70, message = "customerProfile.shortNameTh must not > 70")
    @JsonDeserialize(using = StringDeserializer.class)
    private String shortNameTh;
    @Size(max = 50, message = "customerProfile.titleOtherEn must not > 50")
    @JsonDeserialize(using = StringDeserializer.class)
    private String titleOtherEn;
    @Size(max = 100, message = "customerProfile.firstNameEn must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String firstNameEn;
    @Size(max = 100, message = "customerProfile.middleNameEn must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String middleNameEn;
    @Size(max = 100, message = "customerProfile.lastNameEn must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String lastNameEn;
    @Size(max = 70, message = "customerProfile.shortNameEn must not > 70")
    @JsonDeserialize(using = StringDeserializer.class)
    private String shortNameEn;
    @JsonDeserialize(using = BirthDateDeserializer.class)
    private LocalDate birthDate;
    @Size(max = 1, message = "customerProfile.gender must not > 1")
    @JsonDeserialize(using = StringDeserializer.class)
    private String gender;
    @Size(max = 2, message = "customerProfile.nationality must not > 2")
    @JsonDeserialize(using = StringDeserializer.class)
    private String nationality;
    @Size(max = 20, message = "customerProfile.nationalityOther must not > 20")
    @JsonDeserialize(using = StringDeserializer.class)
    private String nationalityOther;
    @Size(max = 1, message = "customerProfile.maritalStatus must not > 1")
    @JsonDeserialize(using = StringDeserializer.class)
    private String maritalStatus;
    @Size(max = 10, message = "customerProfile.mobileNo must not > 10")
    @JsonDeserialize(using = StringDeserializer.class)
    @Pattern(regexp = REG_EX_NUMERIC,message = "customerProfile.mobileNo must be numeric")
    private String mobileNo;
    @Size(max = 2, message = "customerProfile.mobileCountry must not > 2")
    @JsonDeserialize(using = StringDeserializer.class)
    private String mobileCountry;
    @Size(max = 100, message = "customerProfile.mobileNoReserve must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String mobileNoReserve;
    @Size(max = 100, message = "customerProfile.mobileReserveCountry must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String mobileReserveCountry;
    @Size(max = 100, message = "customerProfile.telephoneNo must not > 100") // TODO: ask SA : regEx Number format ?
    @JsonDeserialize(using = StringDeserializer.class)
    private String telephoneNo;
    @Size(max = 100, message = "customerProfile.officePhone must not > 100") // TODO: ask SA : regEx Number format ?
    @JsonDeserialize(using = StringDeserializer.class) // TODO : should it be numeric
    private String officePhone;
    @Size(max = 50, message = "customerProfile.contactFax must not > 50")
    @JsonDeserialize(using = StringDeserializer.class)
    private String contactFax;
    @Size(max = 50, message = "customerProfile.officeFax must not > 50")
    @JsonDeserialize(using = StringDeserializer.class)
    private String officeFax;
    @Size(max = 100, message = "customerProfile.primaryEmail must not > 100")
    @Email
    private String primaryEmail;
    @Size(max = 100, message = "customerProfile.reserveEmail must not > 100")
    @Email
    private String reserveEmail;
    @Size(max = 20, message = "customerProfile.mailingChannel must not > 20")
    @JsonDeserialize(using = StringDeserializer.class)
    private String mailingChannel;
    @Size(max = 20, message = "customerProfile.mailingPostChannel must not > 20")
    @JsonDeserialize(using = StringDeserializer.class)
    private String mailingPostChannel;
    @Size(max = 13, message = "customerProfile.taxId must not > 13")
    @JsonDeserialize(using = StringDeserializer.class)
    @Pattern(regexp = REG_EX_NUMERIC_13_DIGIT,message = "taxId must be Numeric 13 digits")
    private String taxId;
    @Size(max = 3, message = "customerProfile.customerProfile.occupationId must not > 3")
    @JsonDeserialize(using = StringDeserializer.class)
    private String occupationId;
    @Size(max = 100, message = "customerProfile.occupationOther must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String occupationOther;
    @Size(max = 3, message = "customerProfile.businessTypeId must not > 3")
    @JsonDeserialize(using = StringDeserializer.class)
    private String businessTypeId;
    @Size(max = 100, message = "customerProfile.businessTypeOther must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String businessTypeOther;
    @Size(max = 100, message = "customerProfile.workPosition must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String workPosition;
    @Size(max = 70, message = "customerProfile.companyNameTh must not > 70")
    @JsonDeserialize(using = StringDeserializer.class)
    private String companyNameTh;
    @Size(max = 70, message = "customerProfile.companyNameEn must not > 70")
    @JsonDeserialize(using = StringDeserializer.class)
    private String companyNameEn;
    @Digits(integer = 16, fraction = 2, message = "customerProfile.income must be (16,2)")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal income;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    @Digits(integer = 16, fraction = 2, message = "customerProfile.incomeOther must be (16,2)")
    private BigDecimal incomeOther;
    @Size(max = 100, message = "customerProfile.sourceOfIncome must not > 100")
    @JsonDeserialize(using = StringWithOutCommonFormatDeserializer.class)
    private String sourceOfIncome;
    @Size(max = 100, message = "customerProfile.sourceOfIncomeOther must not > 100")
    @JsonDeserialize(using = StringWithOutCommonFormatDeserializer.class)
    private String sourceOfIncomeOther;
    @Size(max = 100, message = "countrySourceOfIncome must not > 2") // todo check cis-core
    @JsonDeserialize(using = StringDeserializer.class)
    private String countrySourceOfIncome;
    @JsonDeserialize(using = BooleanDeserializer.class)
    private Boolean investmentKnowledgeFlag;
    @Size(max = 100, message = "investmentPurposeId must not > 100")
    @JsonDeserialize(using = StringWithOutCommonFormatDeserializer.class)
    private String investmentPurposeId;
    @Size(max = 100, message = "customerProfile.investmentPurposeOther must not > 100 ") // check in cis-core
    @JsonDeserialize(using = StringDeserializer.class)
    private String investmentPurposeOther;
    @Size(max = 2, message = "customerProfile.investorClass must not > 2") // if null set default 9
    @JsonDeserialize(using = StringDeserializer.class)
    private String investorClass;
    @Size(max = 2, message = "customerProfile.qualifiedInvestorFlag must not > 1")// if null set default N
    @JsonDeserialize(using = StringDeserializer.class)
    private String qualifiedInvestorFlag; // LOvs : [Y/N]
    @JsonDeserialize(using = BooleanDeserializer.class)
    private Boolean politicalPersonFlag;
    @Size(max = 50, message = "customerProfile.politicalPosition must not > 50")
    @JsonDeserialize(using = StringDeserializer.class)
    private String politicalPosition;
    @JsonDeserialize(using = BooleanDeserializer.class)
    private Boolean isHavePoliticalRelationship;
     @Size(max = 2, message = "customerProfile.documentLanguage must not > 2") // LOVs:  TH EN
    @JsonDeserialize(using = StringDeserializer.class)
    private String documentLanguage;
    @Size(max = 100, message = "customerProfile.specialCondition must not > 100 ")
    @JsonDeserialize(using = StringDeserializer.class)
    private String specialCondition;
    @Size(max = 20, message = "customerProfile.custStatus must not > 20 ")
    @JsonDeserialize(using = StringDeserializer.class)
    private String custStatus;

}
