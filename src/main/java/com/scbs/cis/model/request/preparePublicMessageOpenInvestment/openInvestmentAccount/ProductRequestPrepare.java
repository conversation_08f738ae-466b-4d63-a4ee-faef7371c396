package com.scbs.cis.model.request.preparePublicMessageOpenInvestment.openInvestmentAccount;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.scbs.cis.model.request.preparePublicMessageOpenInvestment.customer.CustomerAoRequestPrepare;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductRequestPrepare {

    private String accountNumber;
    private String productType;
    private String accountType;
    private String customerCode;

    @JsonProperty("fcnAccount")
    private FcnAccountRequestPrepare fcnAccountRequestPrepare;

    @JsonProperty("sbaAccount")
    private SbaAccountRequestPrepare sbaAccountRequestPrepare;

    @JsonProperty("bsbAccount")
    private BsbAccountRequestPrepare bsbAccountRequestPrepare;

    @JsonProperty("serviceList")
    private List<ServiceRequestOpenInvestmentPrepare> serviceRequestPrepareList;

    @JsonProperty("ao")
    private CustomerAoRequestPrepare customerAoRequestPrepare;

    @JsonProperty("bankAccountUsage")
    private List<BankAccountUsageRequestOpenInvestmentPrepare> bankAccountUsageRequestOpenInvestmentPrepareList;

    @JsonProperty("investmentLimitAcct")
    private InvestmentLimitAcctRequestPrepare investmentLimitAcctRequestPrepare;
}