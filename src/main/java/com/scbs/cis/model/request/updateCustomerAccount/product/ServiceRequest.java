package com.scbs.cis.model.request.updateCustomerAccount.product;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.StringDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ServiceRequest {


    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 20,message = "ServiceRequestPrepare.serviceType must not > 20")
    private String serviceType;

    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 20,message = "ServiceRequestPrepare.serviceStatus must not > 20")
    private String serviceStatus;

    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 20,message = "ServiceRequestPrepare.profileId must not > 20")
    private String profileId;


}
