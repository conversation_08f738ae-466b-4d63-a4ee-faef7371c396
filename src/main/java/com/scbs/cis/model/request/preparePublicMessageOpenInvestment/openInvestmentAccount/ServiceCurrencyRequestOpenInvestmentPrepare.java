package com.scbs.cis.model.request.preparePublicMessageOpenInvestment.openInvestmentAccount;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ServiceCurrencyRequestOpenInvestmentPrepare {

    private String serviceSubAccount;
    private String currency;
    private String serviceSubAccountStatus;

}