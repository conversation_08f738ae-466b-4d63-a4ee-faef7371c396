package com.scbs.cis.model.request.preparePublicMessageUpdateCustomerProfile.updateCustomerProfileToCisCore;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExistingProfileRequest {
    @JsonProperty("customerProfile")
    private CustomerProfileRequest customerProfileRequest;
    @JsonProperty("customerCard")
    private CustomerCardRequest customerCardRequest;
    @JsonProperty("relationship")
    private List<RelationshipRequest> relationshipRequest;
    @JsonProperty("address")
    private List<AddressRequest> addressRequestList;
    @JsonProperty("vulnerable")
    private VulnerableRequest vulnerableRequest;
    @JsonProperty("profileCisUid")
    private ProfileCisUidRequest profileCisUidRequest;
    @JsonProperty("addressCisUid")
    private AddressCisUidRequest addressCisUidRequest;
    @JsonProperty("onboardingProfiles")
    private OnboardingProfileRequest onboardingProfileRequest;
    @JsonProperty("suitability")
    private SuitabilityRequest suitabilityRequest;
    @JsonProperty("customerKyc")
    private CustomerKycRequest customerKycRequest;
    @JsonProperty("customerFatca")
    private CustomerFatcaRequest customerFatcaRequest;
    @JsonProperty("customerAo")
    private List<CustomerAoRequest> customerAoRequestList;
    @JsonProperty("referralInfo")
    private ReferralInfoRequest referralInfoRequest;
    @JsonProperty("accountDetails")
    private List<AccountDetailRequest> accountDetailRequestList;


}
