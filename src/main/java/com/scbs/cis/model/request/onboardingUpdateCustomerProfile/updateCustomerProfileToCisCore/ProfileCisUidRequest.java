package com.scbs.cis.model.request.onboardingUpdateCustomerProfile.updateCustomerProfileToCisCore;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProfileCisUidRequest {
    private String	mobileNo;
    private String	mobileCountry;
    private String	primaryEmail;
    private String	reserveEmail;
    private String	mailingChannel;
    private String	mailingPostChannel;
    private String	aum;
    private String	segmentCode;
    private String  segmentDescription;
}
