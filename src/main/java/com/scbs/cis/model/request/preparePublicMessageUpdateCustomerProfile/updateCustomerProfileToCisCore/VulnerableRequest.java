package com.scbs.cis.model.request.preparePublicMessageUpdateCustomerProfile.updateCustomerProfileToCisCore;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class VulnerableRequest {
    private Boolean	isVulByAge;
    private Boolean	isVulByInvestmentKnowledge;
    private Boolean	isDisabledVul;
    private String remark;

}
