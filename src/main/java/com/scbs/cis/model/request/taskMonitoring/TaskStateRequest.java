package com.scbs.cis.model.request.taskMonitoring;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.BooleanDeserializer;
import com.scbs.cis.deserializer.StringDeserializer;
import com.scbs.cis.deserializer.StringWithOutCommonFormatDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_EMPTY)
public class TaskStateRequest {

    @NotEmpty(message = "taskStateId cannot be empty")
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 36,message = "taskStateId must not > 36")
    private String taskStateId;
    @NotEmpty(message = "taskId cannot be empty")
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 36,message = "taskId must not > 36")
    private String taskId;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 200,message = "taskStateName must not > 200")
    private String taskStateName;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 50,message = "taskStateStatus must not > 50")
    private String taskStateStatus;
    @JsonDeserialize(using = StringWithOutCommonFormatDeserializer.class)
    private String requestMessage;
    @JsonDeserialize(using = StringWithOutCommonFormatDeserializer.class)
    private String responseMessage;
    @JsonDeserialize(using = BooleanDeserializer.class)
    private Boolean retryableFlag;

}
