package com.scbs.cis.model.request.prepareUploadDocumentData;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerDetail {

    private CustomerProfile customerProfile;
    private CustomerCard customerCard;
    @JsonProperty("accountDetails")
    private List<AccountDetail> accountDetailList;
}
