package com.scbs.cis.model.request.openInvestmentSmeBoard;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.BigDecimalDeserializer;
import com.scbs.cis.deserializer.StringDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class InvestmentLimitAcct {

    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal limitAmount;
    @JsonDeserialize(using = StringDeserializer.class)
    private String limitType;
}
