package com.scbs.cis.model.request.createCisuidAccount;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.scbs.cis.model.RequestCommonHeaderModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CreateInvestmentAccountRequest {


    private RequestCommonHeaderModel requestCommonHeaderModel;
    private CreateInvestmentAccountDetailRequest requestMessage;


}
