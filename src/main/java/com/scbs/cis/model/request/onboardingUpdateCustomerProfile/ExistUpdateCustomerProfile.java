package com.scbs.cis.model.request.onboardingUpdateCustomerProfile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scbs.cis.deserializer.LocalDateDeserializer;
import com.scbs.cis.deserializer.StringDeserializer;
import com.scbs.cis.model.request.onboardingUpdateCustomerProfile.updateCustomerProfileToCisCore.*;
import com.scbs.cis.serializer.LocalDateSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.USE_DEFAULTS)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExistUpdateCustomerProfile {

    @NotEmpty(message = "createInvestmentAccountRequest.referenceId must not be Empty")
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 36,message = "createInvestmentAccountRequest.referenceId must not > 36")
    private String referenceId;
    @NotEmpty(message = "createInvestmentAccountRequest.applicationId must not be Empty")
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 50,message = "createInvestmentAccountRequest.applicationId must not > 50")
    private String applicationId;
    @NotNull(message = "createInvestmentAccountRequest.applicationDate must not be Empty")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate applicationDate;
    @NotEmpty(message = "createInvestmentAccountRequest.customerId must not be Empty")
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 12,message = "createInvestmentAccountRequest.customerId must not be Empty")
    private String customerId;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 15 ,message = "createInvestmentAccountRequest.cisUid must not > 15")
    private String cisUid;
    @NotEmpty(message = "createInvestmentAccountRequest.partner must not be Empty")
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 20,message = "createInvestmentAccountRequest.partner must not > 20")
    private String partner;
    @JsonProperty("customerProfile")
    private CustomerProfileRequest customerProfileRequest;
    @JsonProperty("customerCard")
    private CustomerCardRequest customerCardRequest;
    @JsonProperty("relationship")
    private List<RelationshipRequest> relationshipRequest;
    @JsonProperty("address")
    private List<AddressRequest> addressRequestList;
    @JsonProperty("vulnerable")
    private VulnerableRequest vulnerableRequest;
    @JsonProperty("profileCisUid")
    private ProfileCisUidRequest profileCisUidRequest;
    @JsonProperty("addressCisUid")
    private AddressCisUidRequest addressCisUidRequest;
    @JsonProperty("onboardingProfiles")
    private OnboardingProfileRequest onboardingProfileRequest;
    @JsonProperty("suitability")
    private SuitabilityRequest suitabilityRequest;
    @JsonProperty("customerKyc")
    private CustomerKycRequest customerKycRequest;
    @JsonProperty("customerAo")
    private List<CustomerAoRequest> customerAoRequestList;
    @JsonProperty("referralInfo")
    private ReferralInfoRequest referralInfoRequest;
    @JsonProperty("accountDetails")
    private List<AccountDetailRequest> accountDetailRequestList;
}
