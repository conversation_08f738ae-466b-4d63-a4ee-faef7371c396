package com.scbs.cis.model.request.createInvestmentAccount;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.BigDecimalDeserializer;
import com.scbs.cis.deserializer.StringDeserializer;
import com.scbs.cis.deserializer.StringWithOutCommonFormatDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;

import static com.scbs.cis.constants.CisConstants.REG_EX_NUMERIC;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProfileCisUidRequest {

    @Pattern(regexp = REG_EX_NUMERIC)
    @JsonDeserialize(using = StringWithOutCommonFormatDeserializer.class)
    @Size(max = 10,message = "ProfileCisUidRequest.mobileNo must not > 10")
    private String mobileNo;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 2,message = "ProfileCisUidRequest.mobileCountry must not > 2")
    private String mobileCountry;
    @JsonDeserialize(using = StringWithOutCommonFormatDeserializer.class)
    @Email(message = "ProfileCisUidRequest.primaryEmail must be Email format" )
    @Size(max = 100,message = "ProfileCisUidRequest.primaryEmail must not > 100")
    private String primaryEmail;
    @JsonDeserialize(using = StringWithOutCommonFormatDeserializer.class)
    @Email(message = "ProfileCisUidRequest.reserveEmail must be Email format" )
    @Size(max = 100,message = "ProfileCisUidRequest.reserveEmail must not > 100")
    private String reserveEmail;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 20,message = "ProfileCisUidRequest.mailingChannel must not > 20")
    private String mailingChannel;
    @JsonDeserialize(using = StringWithOutCommonFormatDeserializer.class)
    @Size(max = 20,message = "ProfileCisUidRequest.mailingPostChannel must not > 20")
    private String mailingPostChannel;
    @Digits(integer = 16, fraction = 2, message = "ProfileCisUidRequest.aum must be (16,2)")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal aum;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 10,message = "ProfileCisUidRequest.segmentCode must not > 10")
    private String segmentCode;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 100,message = "ProfileCisUidRequest.segmentDescription must not > 100")
    private String segmentDescription;

}
