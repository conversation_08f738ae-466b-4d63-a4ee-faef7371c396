package com.scbs.cis.model.request.preparePublicMessageUpdateCustomerProfile.updateCustomerProfileToCisCore;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scbs.cis.deserializer.LocalDateDeserializer;
import com.scbs.cis.serializer.LocalDateSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerAoRequest {
    private String	aoId;
    private String	productMarket;
    private String	aoType;
    private String	customerAoPeriodType;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate	effectiveDate;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate endDate;
    private String	icLicense;
    private String	teamId;
    private String	traderIdEquityMain;
    private String	traderIIdTfexMain;
    private String	supEmployeeId;
    private String	branch;

}
