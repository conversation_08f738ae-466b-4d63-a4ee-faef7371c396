package com.scbs.cis.model.request.enterpriseApi.fatcaValidation;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FatcaValidationRequest {

    @JsonProperty("Appid")
    private String appId;
    @JsonProperty("Userid")
    private String userId;
    private int page = 0;
    @JsonProperty("idnumber")
    private String idNumber;
    @JsonProperty("idtype")
    private String idType;

}
