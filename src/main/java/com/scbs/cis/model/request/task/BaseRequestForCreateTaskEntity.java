package com.scbs.cis.model.request.task;

import lombok.Data;

import java.time.LocalDate;

@Data
public class BaseRequestForCreateTaskEntity {

    private String referenceId;
    private String partner;
    private String cisUidFromBody;
    private String cisUidFromHeader;
    private String customerIdFromBody;
    private String customerIdFromHeader;
    private LocalDate applicationDate;
    private String applicationId;
    private String correlationId;
    private String channel;
    private String resourceOwnerId;
    private String requestUid;
    private String cardNumber;

}
