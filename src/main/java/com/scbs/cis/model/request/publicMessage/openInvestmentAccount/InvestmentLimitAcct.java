package com.scbs.cis.model.request.publicMessage.openInvestmentAccount;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.BigDecimalDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class InvestmentLimitAcct {
    @NotNull
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal limitAmount;

    @Size(max = 10,message = "openInvestmentAccount.productList[].investmentLimitAcct.limitType must not > 10")
    private String limitType;
}