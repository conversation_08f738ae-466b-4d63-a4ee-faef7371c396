package com.scbs.cis.model.request.openInvestmentDrxDone;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OpenInvestmentAccountRequest {

    @JsonProperty("productList")
    List<ProductRequest> productRequestList;

}
