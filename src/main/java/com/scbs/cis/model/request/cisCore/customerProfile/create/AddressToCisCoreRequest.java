package com.scbs.cis.model.request.cisCore.customerProfile.create;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AddressToCisCoreRequest {


    private String addressType;
    private String no;
    private String building;
    private String floor;
    private String roomNo;
    private String soi;
    private String village;
    private String moo;
    private String road;
    private String subdistrictName;
    private String subdistrictCode;
    private String districtName;
    private String districtCode;
    private String provinceName;
    private String provinceCode;
    private String countryCode;
    private String zipCode;
    private String department;
    private String remark;

}
