package com.scbs.cis.model.request.preparePublicMessageUpdateCustomerProfile.PrepareUploadDocumentDataForUpdateCustomerProfile;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.StringDeserializer;

import jakarta.validation.constraints.Size;

public class CustomerProfile {
    @Size(max = 20, message = "customerProfile.customerType must not > 20")
    @JsonDeserialize(using = StringDeserializer.class)
    private String customerType;
    @Size(max = 3, message = "customerProfile.titleId must not > 3")
    @JsonDeserialize(using = StringDeserializer.class)
    private String titleId;
    @Size(max = 50, message = "customerProfile.titleOtherTh must not > 50")
    @JsonDeserialize(using = StringDeserializer.class)
    private String titleOtherTh;
    @Size(max = 100, message = "customerProfile.firstNameTh must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String firstNameTh;
    @Size(max = 100, message = "customerProfile.middleNameTh must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String middleNameTh;

}
