package com.scbs.cis.model.request.openInvestmentWalletProfile;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OpenInvestmentWalletProfileDoneRequest {

    private String status;
    private String walletId;
    private String walletIdMarkup;
    private String cisUid;
    private String cisCustId;

}
