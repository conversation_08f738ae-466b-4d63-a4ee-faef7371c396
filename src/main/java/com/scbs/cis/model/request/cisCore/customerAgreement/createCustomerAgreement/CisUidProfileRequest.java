package com.scbs.cis.model.request.cisCore.customerAgreement.createCustomerAgreement;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scbs.cis.serializer.LocalDateSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CisUidProfileRequest {

    private String mobileTelNo;
    private String mobileTelCountry;
    private String emailAddressPrimary;
    private String emailAddressReserve;
    private String mailingChannel;
    private String mailingPostChannel;
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate idCreateDate;
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate idCloseDate;
    private String status;

}
