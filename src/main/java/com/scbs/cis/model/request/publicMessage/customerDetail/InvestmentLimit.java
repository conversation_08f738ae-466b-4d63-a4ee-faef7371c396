package com.scbs.cis.model.request.publicMessage.customerDetail;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.BigDecimalDeserializer;
import com.scbs.cis.deserializer.StringDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InvestmentLimit {
    @Digits(integer = 13,fraction = 2 ,message = "investmentLimit.limit amount must be (13,2)")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal limitAmount;
    @Size(max = 10,message = "investmentLimit.limitType must not > 10")
    @JsonDeserialize(using= StringDeserializer.class)
    private String limitType;
}