package com.scbs.cis.model.request.updateKyc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scbs.cis.deserializer.LocalDateTimeDeserializer;
import com.scbs.cis.serializer.LocalDateTimeSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdateKycRequest {

    private String cardNo;
    private String nationCode;
    private String passportCountryCode;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime kycReviewDate;
    private String kycLevel;
    private String kycSuitabilityGroupFlag;
    private String titleCode;
    private String titleOtherTh;
    private String titleOtherEn;
    private String firstNameTh;
    private String lastNameTh;
    private String firstNameEn;
    private String lastNameEn;
    private String mobileNo;
    private String telephoneNo;
    private String occupationCode;
    private String occupationOther;
    private String businessTypeCode;
    private String businessTypeOther;
    private String workPlace;
    private String workPosition;
    private String spFirstNameTh;
    private String spLastNameTh;
    private String spFirstNameEn;
    private String spLastNameEn;
    private String spCardNo;
    private String spNationCode;
    private String spPassportCountryCode;
    private boolean politicalPersonFlag;
    private boolean investmentForeignFlag;
    private boolean residenceForeignFlag;
    private boolean controlPersonFlag;
    private boolean beneficiaryFlag;
    private String investmentPurposeCode;
    private String investmentPurposeOther;

    @JsonProperty("customerAddresses")
    private List<CustomerAddressRequest> customerAddresses;
    @JsonProperty("customerPoliticalPersons")
    private List<CustomerPoliticalPersonsRequest> customerPoliticalPersons;
    @JsonProperty("customerInvestmentForeigns")
    private List<CustomerInvestmentForeignsRequest> customerInvestmentForeigns;
    @JsonProperty("customerResidenceForeigns")
    private List<CustomerResidenceForeignsRequest> customerResidenceForeigns;
    @JsonProperty("customerControlPersons")
    private List<CustomerControlPersonsRequest> customerControlPersons;
    @JsonProperty("customerBeneficiaries")
    private List<CustomerBeneficiariesRequest> customerBeneficiaries;
}
