package com.scbs.cis.model.request.publicMessage.customerDetail;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scbs.cis.deserializer.BirthDateDeserializer;
import com.scbs.cis.deserializer.BooleanDeserializer;
import com.scbs.cis.deserializer.LocalDateDeserializer;
import com.scbs.cis.deserializer.StringDeserializer;
import com.scbs.cis.serializer.LocalDateSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;

import static com.scbs.cis.constants.CisConstants.REG_EX_ALPHA_NUMERIC;
import static com.scbs.cis.constants.CisConstants.REG_EX_NUMERIC;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor

@JsonIgnoreProperties(ignoreUnknown = true)
public class Relationships {

    @Size(max = 20 , message = "relationship.relationshipType must not > 20")
    @JsonDeserialize(using = StringDeserializer.class)
    private String relationshipType;
    @Size(max = 3 , message = "relationship.relationshipId must not > 3")
    @JsonDeserialize(using = StringDeserializer.class)
    private String relationshipId;
    @Size(max = 50, message = "relationship.relationshipOther must not > 50")
    @JsonDeserialize(using = StringDeserializer.class)
    private String relationshipOther;
    @Size(max = 3, message = "relationship.titleId must not > 3")
    @JsonDeserialize(using = StringDeserializer.class)
    private String titleId;
    @Size(max = 50, message = "relationship.titleOtherTh must not > 50")
    @JsonDeserialize(using = StringDeserializer.class)
    private String titleOtherTh;
    @Size(max = 100, message = "relationship.firstNameTh must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String firstNameTh;
    @Size(max = 100, message = "relationship.middleNameTh must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String middleNameTh;
    @Size(max = 100, message = "relationship.lastNameTh must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String lastNameTh;
    @Size(max = 50, message = "relationship.titleOtherEn must not > 50")
    @JsonDeserialize(using = StringDeserializer.class)
    private String titleOtherEn;
    @Size(max = 100, message = "relationship.firstNameEn must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String firstNameEn;
    @Size(max = 100, message = "relationship.relationship.middleNameEn must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String middleNameEn;
    @Size(max = 100, message = "relationship.relationship.lastNameEn must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String lastNameEn;
    @JsonDeserialize(using = BirthDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate birthDate;
    @Size(max = 1, message = "relationship.gender must not > 1")
    @JsonDeserialize(using = StringDeserializer.class)
    private String gender;
    @Size(max = 2, message = "relationship.nationality must not > 2")
    @JsonDeserialize(using = StringDeserializer.class)
    private String nationality;
    @Size(max = 100, message = "relationship.nationalityOther must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String nationalityOther;
    @Size(max = 3, message = "relationship.cardType must not > 3")
    @JsonDeserialize(using = StringDeserializer.class)
    private String cardType;
    @Size(max = 20, message = "relationship.cardNumber must not > 20")
    @JsonDeserialize(using = StringDeserializer.class)
    @Pattern(regexp = REG_EX_ALPHA_NUMERIC,message = "relationship.cardNumber must be alpha numeric")
    private String cardNumber;
    @Size(max = 2, message = "relationship.cardIssuingCountry must not > 2")
    @JsonDeserialize(using = StringDeserializer.class)
    private String cardIssuingCountry;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate cardIssueDate;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate cardExpiryDate;
    @Size(max = 50, message = "relationship.mobileTelNo must not > 50")
    @JsonDeserialize(using = StringDeserializer.class)
    @Pattern(regexp = REG_EX_NUMERIC,message = "relationship.mobileTelNo must be numeric")
    private String mobileTelNo;
    @Size(max = 100, message = "relationship.telephoneNo must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String telephoneNo;
    @Size(max = 100, message = "relationship.officeTelephoneNo must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    private String officeTelephoneNo;
    @Size(max = 50, message = "relationship.contactFaxNo must not > 50")
    @JsonDeserialize(using = StringDeserializer.class)
    private String contactFaxNo;
    @Size(max = 50, message = "relationship.officeFaxNo must not > 50")
    @JsonDeserialize(using = StringDeserializer.class)
    private String officeFaxNo;
    @Size(max = 100, message = "relationship.emailAddress must not > 100")
    @JsonDeserialize(using = StringDeserializer.class)
    @Email
    private String emailAddress;
    @Size(max = 500, message = "relationship.officeAddress must not > 500")
    @JsonDeserialize(using = StringDeserializer.class)
    private String officeAddress;
    @Size(max = 250, message = "relationship.address1 must not > 250")
    @JsonDeserialize(using = StringDeserializer.class)
    private String address1;
    @Size(max = 250, message = "relationship.address2 must not > 250")
    @JsonDeserialize(using = StringDeserializer.class)
    private String address2;
    @Size(max = 250, message = "relationship.address3 must not > 250")
    @JsonDeserialize(using = StringDeserializer.class)
    private String address3;
    @Size(max = 250, message = "relationship.address4 must not > 250")
    @JsonDeserialize(using = StringDeserializer.class)
    private String address4;
    @Size(max =2, message = "relationship.documentLanguage must not > 2")
    @JsonDeserialize(using = StringDeserializer.class)
    private String documentLanguage; // TODO : LOVs TH EN
    @JsonDeserialize(using = BooleanDeserializer.class)
    private Boolean politicalPersonFlag;
    @Size(max = 250, message = "relationship.politicalPosition must not > 250")
    @JsonDeserialize(using = StringDeserializer.class)
    private String politicalPosition;


}
