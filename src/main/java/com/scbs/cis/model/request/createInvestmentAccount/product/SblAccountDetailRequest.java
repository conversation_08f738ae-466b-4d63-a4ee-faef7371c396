package com.scbs.cis.model.request.createInvestmentAccount.product;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.BigDecimalDeserializer;
import com.scbs.cis.deserializer.StringDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SblAccountDetailRequest { // CM ?

    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 1,message = "SblAccountDetailRequestPrepare.enableFlag must not > 1")
    private String enableFlag;
    @Digits(integer = 16, fraction = 2, message = "SblAccountDetailRequestPrepare.appCreditLine must be (16,2)")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal appCreditLine;

}
