package com.scbs.cis.model.request.cisCore.customerProfile.create;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scbs.cis.serializer.LocalDateSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustCardToCisCoreRequest {

    private String cardType;
    private String cardNumber;
    private String cardIssuingCountry;
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate cardIssueDate;
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate cardExpiryDate;

}


