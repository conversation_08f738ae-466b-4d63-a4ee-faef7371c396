package com.scbs.cis.model.request.taskMonitoring;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.BooleanDeserializer;
import com.scbs.cis.deserializer.IntegerDeserializer;
import com.scbs.cis.deserializer.LocalDateTimeDeserializer;
import com.scbs.cis.deserializer.StringDeserializer;
import com.scbs.cis.deserializer.StringWithOutCommonFormatDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_EMPTY)
public class TaskMonitoringTaskRequest {

    @NotEmpty(message = "taskId cannot be empty")
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 36,message = "taskId must not > 36")
    private String taskId;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 50,message = "referenceId must not > 50")
    private String referenceId;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 50,message = "applicationId must not > 50")
    private String applicationId;
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime applicationDate;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 50,message = "correlationId must not > 50")
    private String correlationId;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 50,message = "requestUid must not > 50")
    private String requestUid;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 15,message = "customerId must not > 15")
    private String customerId;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 20,message = "cardNumber must not > 20")
    private String cardNumber;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 15,message = "cisUid must not > 15")
    private String cisUid;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 20,message = "partner must not > 20")
    private String partner;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 20,message = "channel must not > 20")
    private String channel;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 200,message = "taskName must not > 200")
    private String taskName;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 50,message = "taskStatus must not > 50")
    private String taskStatus;
    @JsonDeserialize(using = StringDeserializer.class)
    @Size(max = 200,message = "taskStatusDescription must not > 200")
    private String taskStatusDescription;
    @JsonDeserialize(using = StringWithOutCommonFormatDeserializer.class)
    private String requestMessage;
    @JsonDeserialize(using = StringWithOutCommonFormatDeserializer.class)
    private String responseMessage;
    @JsonDeserialize(using = BooleanDeserializer.class)
    private Boolean retryableFlag;
    @JsonDeserialize(using = IntegerDeserializer.class)
    @Min(value = 0)
    @Max(value = 9999)
    private Integer systemRetryCount;
}
