package com.scbs.cis.model.request.cisCore.customerProfile.create;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CreateCustomerProfileToCisCoreRequest {

    @Valid
    @NotNull
    @JsonProperty("custProfile")
    private CustProfileToCisCoreRequest custProfileRequest;
    @Valid
    @JsonProperty("addresses")
    private List<AddressToCisCoreRequest> addressToCisCoreRequestList;
    @Valid
    @JsonProperty("custCard")
    private CustCardToCisCoreRequest custCardToCisCoreRequest;
    @Valid
    @JsonProperty("custRelationships")
    private List<CustRelationshipsToCisCoreRequest> custRelationshipsRequestList;
    @Valid
    @JsonProperty("custVulnerable")
    private CustVulnerableToCisCoreRequest customerVulnerableRequest;

}
