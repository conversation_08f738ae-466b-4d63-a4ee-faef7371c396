package com.scbs.cis.model.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.BooleanDeserializer;
import com.scbs.cis.deserializer.StringDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskStateRequest {

    @NotEmpty
    @Size(max = 36 , message = "taskId must not >36")
    @JsonDeserialize(using = StringDeserializer.class)
    private String taskId;
    @NotEmpty
    @Size(max = 36 , message = "taskStateId must not >36")
    @JsonDeserialize(using = StringDeserializer.class)
    private String taskStateId;
    @JsonDeserialize(using = BooleanDeserializer.class)
    private Boolean isRetry;
   @JsonDeserialize(using = StringDeserializer.class)
    private String retryBy;

}
