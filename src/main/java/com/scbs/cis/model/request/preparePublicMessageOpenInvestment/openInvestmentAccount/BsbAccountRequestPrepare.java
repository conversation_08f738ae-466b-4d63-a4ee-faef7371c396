package com.scbs.cis.model.request.preparePublicMessageOpenInvestment.openInvestmentAccount;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scbs.cis.deserializer.LocalDateDeserializer;
import com.scbs.cis.serializer.LocalDateSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class BsbAccountRequestPrepare {

    private String payeeNameTH;
    private String payeeNameEN;
    private String confirmSlipMethod;
    private BigDecimal maxValuePerOrder;
    private Boolean allowBuy;
    private Boolean allowSell;
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate dateOpen;
    private String loginName;


}