package com.scbs.cis.model.request.verifyCreateAccount;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scbs.cis.deserializer.ListStringDeserializer;
import com.scbs.cis.deserializer.StringDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductRequest {

    @NotEmpty
    @Size(max = 20)
    @JsonDeserialize(using = StringDeserializer.class)
    String productType;

    @Size(max = 20)
    @JsonDeserialize(using = StringDeserializer.class)
    String accountType;

   @JsonDeserialize(using = ListStringDeserializer.class)
    List<String> serviceType;
}
