package com.scbs.cis.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.io.Serializable;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResponseModel<T> implements Serializable {

    @JsonProperty("status")
    private StatusModel status;

    @JsonProperty("data")
    private T data;

    @JsonProperty("errors")
    private List<ErrorModel> errors;

    @JsonProperty("errorModel")
    private ErrorModel error;

    @JsonProperty("message")
    private String message;

    public ResponseModel() { }

    public ResponseModel (StatusModel statusModel) {
        this.status = statusModel;
    }


    public ResponseModel(StatusModel statusModel, T data) {
        this.status = statusModel;
        this.data = data;
    }
    public ResponseModel(StatusModel statusModel, String message) {
        this.status = statusModel;
        this.message = message;
    }

    public ResponseModel(StatusModel statusModel,T data, String message) {
        this.status = statusModel;
        this.data = data;
        this.message = message;
    }

    public ResponseModel(StatusModel status, List<ErrorModel> errors) {
        this.status = status;
        this.errors = errors;
    }

    public ResponseModel(StatusModel status, T data,List<ErrorModel> errors) {
        this.status = status;
        this.data = data;
        this.errors = errors;
    }

    public HttpEntity<ResponseModel> buildResponse(HttpStatus httpStatus) {
        return new ResponseEntity<>(new ResponseModel(this.status, this.data), httpStatus);
    }

    public  ResponseEntity  buildResponseEntity (HttpStatus httpStatus) {
        return new ResponseEntity<>(new ResponseModel(this.status,this.data,this.errors), httpStatus);
    }


}

