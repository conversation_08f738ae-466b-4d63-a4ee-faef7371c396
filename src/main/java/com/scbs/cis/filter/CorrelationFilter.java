package com.scbs.cis.filter;

import com.scbs.cis.constants.CisConstants;
import com.scbs.cis.utils.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.OncePerRequestFilter;
import com.invx.cis.toolbox.logger.Log;
import com.invx.cis.toolbox.logger.LogFactory;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

@Configuration
public class CorrelationFilter extends OncePerRequestFilter {
    private final Log log = LogFactory.getLogger(this.getClass());

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Fi<PERSON><PERSON>hain filterChain) throws ServletException, IOException {

        String correlationId = request.getHeader(CisConstants.HEADER_CORRELATION_ID);
        MyServletRequestWrapper httpReq = new MyServletRequestWrapper(request);

        if (null == correlationId ) {
            correlationId = CommonUtil.generateUUID();
            log.info("Correlation is null, Have new generate  ->  {}", correlationId);
            httpReq.addHeader(CisConstants.HEADER_CORRELATION_ID, correlationId);
        }

        if (StringUtils.isEmpty(correlationId)) {
            correlationId = CommonUtil.generateUUID();
            log.info("Correlation is empty, Have new generate  ->  {}", correlationId);
            httpReq.addHeader(CisConstants.HEADER_CORRELATION_ID, correlationId);
        }

        ThreadContext.put(CisConstants.HEADER_CORRELATION_ID, correlationId);
        filterChain.doFilter(httpReq, response);
        ThreadContext.clearAll();
    }
}
