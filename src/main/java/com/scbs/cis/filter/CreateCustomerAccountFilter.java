package com.scbs.cis.filter;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.google.common.io.ByteSource;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.scbs.cis.constants.StatusResponseEnum;
import com.scbs.cis.dao.impl.SubStateDAO;
import com.scbs.cis.dao.impl.TaskDAO;
import com.scbs.cis.dao.impl.TaskStateDAO;
import com.scbs.cis.entity.SubState;
import com.scbs.cis.entity.Task;
import com.scbs.cis.entity.TaskState;
import com.scbs.cis.exception.CISServiceException;
import com.scbs.cis.model.ResponseModel;
import com.scbs.cis.model.StatusModel;
import com.scbs.cis.model.request.task.*;
import com.scbs.cis.utils.CommonUtil;
import com.scbs.cis.utils.RequestUtil;
import com.scbs.cis.utils.ValidateUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingResponseWrapper;
import com.invx.cis.toolbox.logger.Log;
import com.invx.cis.toolbox.logger.LogFactory;


import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import static com.scbs.cis.constants.CisConstants.*;

@Configuration
@AllArgsConstructor
public class CreateCustomerAccountFilter extends OncePerRequestFilter {

    private final Log log = LogFactory.getLogger(this.getClass());
    private TaskDAO taskDAO;
    private RequestUtil requestUtil;
    private ObjectMapper mapper;

    private final SubStateDAO subStateDAO;
    private final TaskStateDAO taskStateDAO;

    private ValidateUtil validateUtil;

    private final String UNDEFINED = "UNDEFINED";
    private final String PROCESSING = "processing";
    private final String FAIL = "fail";
    private final String SUCCESS = "success";

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String requestUID = request.getHeader(HEADER_REQUEST_UUID);
        String correlationId = request.getHeader(HEADER_CORRELATION_ID);
        ThreadContext.put("requestUId",requestUID);
        ThreadContext.put("correlationId",correlationId);

        if (checkURLEndpoint(request)) {
            /**
             * Case url must create task.
             */
            this.stepCreateTask(request, response, filterChain);
        } else {
            // Just continue chain.
            filterChain.doFilter(request, response);
        }
    }

    private boolean checkURLEndpoint(HttpServletRequest request) {
        boolean status = false;
        String path = request.getRequestURI();

        if (path.startsWith("/v1/onboarding/create-customer-account")) {
            status = true;
        }
        return status;
    }

    private String getTaskNameByURL(HttpServletRequest request) {

        String taskName = new String();

        String path = request.getRequestURI();

        if (path.startsWith("/v1/onboarding/verify-create-account")) {
            taskName = "verify-create-account";
        } else if (path.startsWith("/v1/onboarding/create-customer-account")) {
            taskName = "create-customer-account";
        } else if (path.startsWith("/onboarding/create-investment-account")) {
            taskName = "create-investment-account";
        }
        return taskName;
    }

    private void stepCreateTask(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {


        CachedBodyHttpServletRequest req = new CachedBodyHttpServletRequest(request);
        ContentCachingResponseWrapper resp = new ContentCachingResponseWrapper(response);

        try { // todo : move to interceptor  // in filter will for security filter
            Gson gson = new Gson();

            String jsonStrRequestBody = IOUtils.toString(req.getInputStream(), StandardCharsets.UTF_8);
            log.info("requestBody : {}", jsonStrRequestBody);

            JsonObject jsonObjectRequestMessage = gson.fromJson(jsonStrRequestBody, JsonObject.class);
            // TODO : check case sentitive
            String requestMessage = requestUtil.getRequestMessage(req, jsonObjectRequestMessage);

            String taskName = this.getTaskNameByURL(req);

            // Create task
            log.info("==== Start create taskState ======");

            Task task = validateCreateTaskEntity(request, jsonStrRequestBody);

            ThreadContext.put("applicationId",task.getApplicationId());

            String taskId = CommonUtil.generateUUID();
            req.setAttribute(TASK_ID, taskId);
            ThreadContext.put("taskId",taskId);
            log.info("TaskId : {}", taskId);

            task.setId(taskId);
            task.setTaskName(taskName);
            task.setTaskStatus(PROCESSING);
            task.setRequestMessage(requestMessage);
            task.setResponseMessage(null);
            task.setSystemRetryCount(0);
            task.setRetryDatetime(null);

            int result = taskDAO.insert(task);

            if (result <= 0) {
                throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR, StatusResponseEnum.CANNOT_MAKE_CHANGES_TO_DATABASE);
            }

            req.setAttribute("requestMessage",requestMessage);

            filterChain.doFilter(req, resp);


            String jsonResponseMessageString = ByteSource.wrap(resp.getContentAsByteArray()).asCharSource(StandardCharsets.UTF_8).read();
            log.info("response.HttpStatus : {}", response.getStatus());

            String subStateId = (String) req.getAttribute("subStateId");

            if (StringUtils.isNotEmpty(subStateId)) {
                log.info("SubStateId : {}",subStateId);
                result = subStateDAO.update(SubState.builder()
                                .subStateStatus(response.getStatus() == HttpStatus.OK.value() ? SUCCESS : FAIL).build()
                        , SubState.builder().id(subStateId).build());

                if (result <= 0) {
                    log.info("update subState : responseMessage,subStateStatus not success");
                    throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR, StatusResponseEnum.CANNOT_MAKE_CHANGES_TO_DATABASE);
                }
            }


            String taskStateId = (String) req.getAttribute("taskStateId");

            if (StringUtils.isNotEmpty(taskStateId)) {
                log.info("taskStateId : {}",taskStateId);

                result = taskStateDAO.update(TaskState.builder()
//                                .responseMessage(jsonResponseMessageString)
                                .responseMessage(null)
                                .taskStateStatus(response.getStatus() == HttpStatus.OK.value() ? SUCCESS : FAIL)
                                .build()
                        , TaskState.builder().id(taskStateId).build());

                if (result <= 0) {
                    log.info("update taskSate : responseMessage,taskStateStatus not success");
                    throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR, StatusResponseEnum.CANNOT_MAKE_CHANGES_TO_DATABASE);
                }

            }


            result = taskDAO.update(Task.builder()
                            .responseMessage(jsonResponseMessageString)
                            .taskStatus(response.getStatus() == HttpStatus.OK.value() ? SUCCESS : FAIL)
                            .build()
                    , Task.builder()
                            .id(taskId)
                            .build());

            if (result <= 0) {
                log.info("update task : responseMessage,taskStatus not success");
                throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR, StatusResponseEnum.CANNOT_MAKE_CHANGES_TO_DATABASE);
            }

        } catch (CISServiceException cisServiceException) {

            Gson gson = new Gson();
            // Reset Response
            resp.reset();

            int code = cisServiceException.getStatusResponse().getCode();
            String desc = cisServiceException.getStatusResponse().getMessage();

            // Custom Handle response on filter
            ResponseModel responseModel = new ResponseModel();
            responseModel.setStatus(new StatusModel(code, desc));
            String json = gson.toJson(responseModel);
            log.info("{}", json);
            resp.setStatus(cisServiceException.getHttpStatus().value());
            resp.setContentType(CONTENT_TYPE_APPLICATION_JSON);
            resp.getWriter().write(json);

        } finally {
            resp.copyBodyToResponse();
        }


    }

    private Task validateCreateTaskEntity(HttpServletRequest request, String jsonStrRequestBody) { // TODO : make try catch its own method

        Task task = new Task();
        try {
            String correlationId = request.getHeader(HEADER_CORRELATION_ID);
            validateUtil.validateRequestSizeCondition(correlationId, ">", 36, "CorrelationId");
            task.setCorrelationId(correlationId);
        } catch (Exception e) {
            log.error("Exception : {}", e.toString());
            log.info("SET CorrelationId TO : {}", UNDEFINED);
            task.setCorrelationId(UNDEFINED);
        }
        try {
            String customerId = request.getHeader(HEADER_CUSTOMER_ID);
            validateUtil.validateRequestSizeCondition(customerId, ">", 15, "CustomerId");
            task.setCustomerId(customerId);
        } catch (Exception e) {
            log.error("Exception : {}", e.toString());
            log.info("SET CustomerIdFromHeader TO : {}", UNDEFINED);
            task.setCustomerId(UNDEFINED);
        }
        try {
            String cisUid = request.getHeader(HEADER_CIS_UID);
            validateUtil.validateRequestSizeCondition(cisUid, ">", 15, "CisUidData");
            task.setCisUid(cisUid);
        } catch (Exception e) {
            log.error("Exception : {}", e.toString());
            log.info("SET CisUidFromHeader TO : {}", UNDEFINED);
            task.setCisUid(UNDEFINED);
        }
        try {
            String applicationReference = request.getHeader(HEADER_APPLICATION_REFERENCE);
            validateUtil.validateRequestSizeCondition(applicationReference, ">", 20, "ApplicationReference/Channel");
            task.setChannel(applicationReference);
        } catch (Exception e) {
            log.error("Exception : {}", e.toString());
            log.info("SET Channel TO : {}", UNDEFINED);
            task.setChannel(UNDEFINED);
        }
        try {
            String requestUID = request.getHeader(HEADER_REQUEST_UUID);
            validateUtil.validateRequestSizeCondition(requestUID, ">", 36, "RequestUID");
            task.setRequestUid(requestUID);
        } catch (Exception e) {
            log.error("Exception : {}", e.toString());
            log.info("SET RequestUid TO : {}", UNDEFINED);
            task.setRequestUid(UNDEFINED);
        }


        try {
            mapper = new ObjectMapper();
            ApplicationDateModel model = mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).readValue(jsonStrRequestBody, ApplicationDateModel.class);
            if (ObjectUtils.isNotEmpty(model.getApplicationDate())) {
                task.setApplicationDate(LocalDate.parse(model.getApplicationDate(), DateTimeFormatter.ofPattern(DATE_FORMAT)));
            }

        } catch (Exception ex) {
            log.error("Get ApplicationDate form JSON payload. Exception -> {}", String.valueOf(ex));
            task.setApplicationDate(LocalDate.parse("1970-01-01", DateTimeFormatter.ofPattern(DATE_FORMAT)));
        }

        try {
            mapper = new ObjectMapper();
            JsonNode reqNode = mapper.readTree(jsonStrRequestBody);
            JsonNode cardDataNode = reqNode.at("/customerCard");
            if (cardDataNode.has("cardNumber")) {
                String cardNumber = cardDataNode.get("cardNumber").asText();
                validateUtil.validateRequestSizeCondition(cardNumber, ">", 20, "requestBody.cardNumber");
                task.setCardNumber(cardNumber);
            }
        } catch (Exception ex) {
            log.error("Get Card Number form JSON payload. Exception -> {}", String.valueOf(ex));
            task.setCardNumber(UNDEFINED);
        }


        try {
            mapper = new ObjectMapper();
            ReferenceIdRequestModel model = mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).readValue(jsonStrRequestBody, ReferenceIdRequestModel.class);
            validateUtil.validateRequestSizeCondition(model.getReferenceId(), ">", 50, "requestBody.referenceId");
            task.setReferenceId(model.getReferenceId());
        } catch (Exception ex) {
            log.error("Get ReferenceId form JSON payload. Exception -> {}", String.valueOf(ex));
            task.setReferenceId(UNDEFINED);
        }

        try {
            mapper = new ObjectMapper();
            ApplicationIdRequestModel model = mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).readValue(jsonStrRequestBody, ApplicationIdRequestModel.class);
            validateUtil.validateRequestSizeCondition(model.getApplicationId(), ">", 50, "requestBody.applicationId");
            task.setApplicationId(model.getApplicationId());
        } catch (Exception ex) {
            log.error("Get Application Id form JSON payload. Exception -> {}", String.valueOf(ex));
            task.setApplicationId(UNDEFINED);
        }

        try {
            mapper = new ObjectMapper();
            PartnerRequestModel model = mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).readValue(jsonStrRequestBody, PartnerRequestModel.class);
            validateUtil.validateRequestSizeCondition(model.getPartner(), ">", 20, "requestBody.partner");
            task.setPartner(model.getPartner());
        } catch (Exception ex) {
            log.error("Get partner form JSON payload. Exception -> {}", String.valueOf(ex));
            task.setPartner(UNDEFINED);
        }


        return task;
    }


}
