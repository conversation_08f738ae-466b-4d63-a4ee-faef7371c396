package com.scbs.cis.dao.impl;

import static com.scbs.cis.constants.CisConstants.SYSTEM;

import com.scbs.cis.entity.TxCancelFlowHist;
import com.scbs.cis.exception.CISServiceException;
import com.scbs.cis.exception.handlers.DAOExceptionHandle;
import com.scbs.cis.utils.DaoUtil;
import com.scbs.cis.utils.SQLCommandHelper;
import com.scbs.cis.utils.ValidateUtil;
import java.time.LocalDateTime;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import com.invx.cis.toolbox.logger.Log;
import com.invx.cis.toolbox.logger.LogFactory;

@Repository
@RequiredArgsConstructor
public class TxCancelFlowHistDAO extends DAOExceptionHandle {

    private final Log log = LogFactory.getLogger(this.getClass());

    private final JdbcTemplate jdbcTemplate;
    private final DaoUtil daoUtil;

    public int insert(TxCancelFlowHist vo) throws CISServiceException {
        int result = 0;
        try {
            log.info("TxCancelFlowHist : {}", vo);
            LocalDateTime today = LocalDateTime.now();
            if (ValidateUtil.isEmptyOrNull(vo.getCancelledBy())) {
                vo.setCancelledBy(SYSTEM);
            }
            vo.setCancelledDatetime(today);

            SQLCommandHelper sqlCommandHelper = daoUtil.insert(vo);
            log.info("sql : {}", sqlCommandHelper.getSqlCommand());
            log.info("parameter : {}", sqlCommandHelper.getParameters());
            result = jdbcTemplate.update(sqlCommandHelper.getSqlCommand(), sqlCommandHelper.getParameters());
        } catch (Exception ex) {
            super.setErrorDAO(ex);
        }
        log.info("insert TxCancelFlowHist result : {}", result);
        return result;
    }

}
