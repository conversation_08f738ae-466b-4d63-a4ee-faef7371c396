package com.scbs.cis.dao.impl;

import com.scbs.cis.entity.CountryMapping;
import com.scbs.cis.entity.OccupationMapping;
import com.scbs.cis.exception.CISServiceException;
import com.scbs.cis.exception.handlers.DAOExceptionHandle;
import com.scbs.cis.utils.DaoUtil;
import com.scbs.cis.utils.SQLCommandHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import com.invx.cis.toolbox.logger.Log;
import com.invx.cis.toolbox.logger.LogFactory;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

@Repository
@RequiredArgsConstructor
public class OccupationMappingDAO extends DAOExceptionHandle {

    private final Log log = LogFactory.getLogger(this.getClass());
    private final DaoUtil daoUtil;
    private final JdbcTemplate jdbcTemplate;

    public List<OccupationMapping> find(OccupationMapping vo) throws CISServiceException {
        List< OccupationMapping> list = new ArrayList<>();

        try {

            SQLCommandHelper sqlCmdHelper = daoUtil.findAND(vo);
            list = jdbcTemplate.query(sqlCmdHelper.getSqlCommand(), occupationConfigRowMapper,sqlCmdHelper.getParameters());

        } catch (Exception ex) {
            super.setErrorDAO(ex);
        }

        return list;
    }

    final RowMapper<OccupationMapping> occupationConfigRowMapper = new RowMapper<OccupationMapping>() {
        @Override
        public  OccupationMapping mapRow(ResultSet rs, int rowNum) throws SQLException {
            final  OccupationMapping resVO = new  OccupationMapping();
            resVO.setCodeCis(rs.getString("code_cis"));
            resVO.setCodeCdb(rs.getString("code_cdb"));
            return resVO;
        }
    };
}
