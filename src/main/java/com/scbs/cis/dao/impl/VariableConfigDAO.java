package com.scbs.cis.dao.impl;

import com.scbs.cis.constants.StatusResponseEnum;
import com.scbs.cis.dao.AbstractJdbcService;
import com.scbs.cis.dao.intf.IBasicDAO;
import com.scbs.cis.entity.VariableConfig;
import com.scbs.cis.exception.CISServiceException;
import com.scbs.cis.utils.DaoUtil;
import com.scbs.cis.utils.SQLCommandHelper;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

@Repository
public class VariableConfigDAO extends AbstractJdbcService implements IBasicDAO<VariableConfig>  {
    DaoUtil daoUtil;

    public static final String TABLE = "variable_config";
    public static final String VARIABLE_NAME = "variable_name";
    public static final String CHANNEL_NAME = "channel_name";
    public static final String VALUE = "value";

    private static final StringBuilder sqlFieldVariableConfig = new StringBuilder();

    static {
        sqlFieldVariableConfig.append(VARIABLE_NAME + COMMA);
        sqlFieldVariableConfig.append(CHANNEL_NAME + COMMA);
        sqlFieldVariableConfig.append(VALUE);
    }
    public VariableConfigDAO(JdbcTemplate jdbcTemplate, DaoUtil daoUtil) {
        super.setJdbcTemplate(jdbcTemplate);
        this.daoUtil = daoUtil;
    }
    @Override
    public List<VariableConfig> find(VariableConfig vo) throws CISServiceException {
        List<VariableConfig> list = new ArrayList<>();
        try {

            SQLCommandHelper sqlCmdHelper = new SQLCommandHelper();
            sqlCmdHelper = daoUtil.findAND(vo);
            list = super.findByNativeSql(sqlCmdHelper, getVariableConfig);

        } catch (Exception ex) {
            super.setErrorDAO(ex);
        }

        return list;
    }

    @Override
    public int insert(VariableConfig vo) throws CISServiceException {
        return 0;
    }

    @Override
    public int update(VariableConfig vo) throws CISServiceException {
        return 0;
    }

    @Override
    public int delete(VariableConfig vo) throws CISServiceException {
        return 0;
    }
    public VariableConfig findByVariableAndChannel(VariableConfig vo) throws CISServiceException {
        List<VariableConfig> variableConfig = new ArrayList<>();
        try {
            CheckFlagCause flagCause = new CheckFlagCause();
            StringBuilder sql = new StringBuilder();
            List<Object> parameters = new ArrayList<>();

            sql.append(SELECT);
            sql.append(NEWLINE + sqlFieldVariableConfig.toString());
            sql.append(NEWLINE + FROM + super.getSchema(TABLE));
            sql.append(NEWLINE + WHERE + SPACE + VARIABLE_NAME + EQUAL + APOSTROPHE + vo.getVariableName() + APOSTROPHE);
            sql.append(NEWLINE + AND + SPACE + PARENTHESES_OPEN + CHANNEL_NAME + EQUAL + APOSTROPHE + vo.getChannelName() + APOSTROPHE);
            sql.append(NEWLINE + OR + SPACE + CHANNEL_NAME + IS_NULL + OR + CHANNEL_NAME + EQUAL + APOSTROPHE + DEFAULT + APOSTROPHE + PARENTHESES_CLOSE);

            SQLCommandHelper sqlCmdHelper = new SQLCommandHelper();
            sqlCmdHelper.setSqlCommand(sql.toString());
            sqlCmdHelper.setParameters(parameters.toArray());

            variableConfig = super.findByNativeSql(sqlCmdHelper, getVariableConfig);

        } catch (Exception ex) {
            super.setErrorDAO(ex);
        }
        if(variableConfig.size()==0){
            throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR, StatusResponseEnum.CANNOT_MAKE_CHANGES_TO_DATABASE);
        }

        return variableConfig.get(0);
    }
    final RowMapper<VariableConfig> getVariableConfig = new RowMapper<VariableConfig>() {
        @Override
        public VariableConfig mapRow(ResultSet rs, int rowNum) throws SQLException {
            final VariableConfig resVO = new VariableConfig();
            resVO.setVariableName(rs.getString(VARIABLE_NAME));
            resVO.setChannelName(rs.getString(CHANNEL_NAME));
            resVO.setValue(rs.getString(VALUE));

            return resVO;
        }
    };
    public List<VariableConfig> getAllFromVariableConfig() throws CISServiceException {
        List<VariableConfig> variableConfig = new ArrayList<>();
        try {
            StringBuilder sql = new StringBuilder();
            List<Object> parameters = new ArrayList<>();

            sql.append(SELECT);
            sql.append(NEWLINE + sqlFieldVariableConfig);
            sql.append(NEWLINE + FROM + super.getSchema(TABLE));

            SQLCommandHelper sqlCmdHelper = new SQLCommandHelper();
            sqlCmdHelper.setSqlCommand(sql.toString());
            sqlCmdHelper.setParameters(parameters.toArray());

            variableConfig = super.findByNativeSql(sqlCmdHelper, getVariableConfig);
        } catch (Exception ex) {
            super.setErrorDAO(ex);
        }
        return variableConfig;
    }
}
