package com.scbs.cis.dao.impl;

import com.scbs.cis.entity.BusinessTypeMapping;
import com.scbs.cis.entity.CardTypeMapping;
import com.scbs.cis.entity.CountryMapping;
import com.scbs.cis.exception.CISServiceException;
import com.scbs.cis.exception.handlers.DAOExceptionHandle;
import com.scbs.cis.utils.DaoUtil;
import com.scbs.cis.utils.SQLCommandHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import com.invx.cis.toolbox.logger.Log;
import com.invx.cis.toolbox.logger.LogFactory;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

@Repository
@RequiredArgsConstructor
public class CardTypeMappingDAO extends DAOExceptionHandle {

    private final Log log = LogFactory.getLogger(this.getClass());
    private final DaoUtil daoUtil;
    private final JdbcTemplate jdbcTemplate;

    public List<CardTypeMapping> find(CardTypeMapping vo) throws CISServiceException {
        List< CardTypeMapping> list = new ArrayList<>();

        try {

            SQLCommandHelper sqlCmdHelper = daoUtil.findAND(vo);
            list = jdbcTemplate.query(sqlCmdHelper.getSqlCommand(), cardTypeMappingConfigRowMapper,sqlCmdHelper.getParameters());

        } catch (Exception ex) {
            super.setErrorDAO(ex);
        }

        return list;
    }

    final RowMapper<CardTypeMapping> cardTypeMappingConfigRowMapper = new RowMapper<CardTypeMapping>() {
        @Override
        public  CardTypeMapping mapRow(ResultSet rs, int rowNum) throws SQLException {
            final  CardTypeMapping resVO = new  CardTypeMapping();
            resVO.setCodeCis(rs.getString("code_cis"));
            resVO.setCodeCdb(rs.getString("code_cdb"));
            return resVO;
        }
    };
}
