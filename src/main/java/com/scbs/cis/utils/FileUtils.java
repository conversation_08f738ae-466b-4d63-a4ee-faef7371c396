package com.scbs.cis.utils;

import com.scbs.cis.constants.StatusResponseEnum;
import com.scbs.cis.exception.CISServiceException;
import io.micrometer.core.instrument.util.IOUtils;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.stream.Collectors;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

@Component
public class FileUtils {

    public static String readFileFromClassPathResourceToString(String pathFile) throws IOException {
        String result;
        try (InputStream resource = new ClassPathResource(pathFile).getInputStream()) {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(resource))) {
                result = reader.lines().collect(Collectors.joining("\n"));
            }
        }
        return result;
    }

    public String getStringFromJsonFile(String path) {

        try {
            ClassPathResource dataResource = new ClassPathResource(path);
            String jsonAsString = IOUtils.toString(dataResource.getInputStream(), StandardCharsets.UTF_8);
            return jsonAsString;

        } catch (Exception ex) {
            CommonUtil.convertStackToStingNotLimit(ex);
            throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                    StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED, "cannot getStringFromJsonFile");
        }
    }


    public static InputStream readResourceAsInputStream(String resourcePath) throws IOException {
        return new ClassPathResource(resourcePath).getInputStream();
    }

    public static File readResourceAsFile(String resourcePath) throws IOException {
        return new ClassPathResource(resourcePath).getFile();
    }

    public static void writeFileByPath(String base64, String targetPath) {
        try {
            InputStream is = new ByteArrayInputStream(Base64.getDecoder().decode(base64));
            File targetFile = new File(targetPath);
            OutputStream outStream = new FileOutputStream(targetFile);

            byte[] buffer = new byte[8 * 1024];
            int bytesRead;
            while ((bytesRead = is.read(buffer)) != -1) {
                outStream.write(buffer, 0, bytesRead);
            }
            org.apache.commons.io.IOUtils.closeQuietly(is);
            org.apache.commons.io.IOUtils.closeQuietly(outStream);

        } catch (Exception exception) { // TODO : check throw ex ?
            CommonUtil.convertStackToStingNotLimit(exception);
        }
    }

    public static void writeFileByPathByte(byte[] base64, String targetPath) {
        try {
            InputStream is = new ByteArrayInputStream(base64);
            File targetFile = new File(targetPath);
            OutputStream outStream = new FileOutputStream(targetFile);

            byte[] buffer = new byte[8 * 1024];
            int bytesRead;
            while ((bytesRead = is.read(buffer)) != -1) {
                outStream.write(buffer, 0, bytesRead);
            }
            org.apache.commons.io.IOUtils.closeQuietly(is);
            org.apache.commons.io.IOUtils.closeQuietly(outStream);

        } catch (Exception exception) {
            CommonUtil.convertStackToStingNotLimit(exception);
        }
    }

}