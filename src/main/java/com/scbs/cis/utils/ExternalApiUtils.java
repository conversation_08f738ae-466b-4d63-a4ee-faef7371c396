package com.scbs.cis.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.gson.Gson;
import com.scbs.cis.constants.CisConstants;
import com.scbs.cis.constants.StatusResponse;
import com.scbs.cis.constants.StatusResponseEnum;
import com.scbs.cis.exception.CISServiceException;
import com.scbs.cis.model.ErrorModel;
import com.scbs.cis.model.ResponseModel;
import com.scbs.cis.model.response.cdb.CDBErrorRespionse;
import com.scbs.cis.model.response.cdb.login.CdbResponseEntity;
import java.net.ConnectException;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import com.invx.cis.toolbox.logger.Log;
import com.invx.cis.toolbox.logger.LogFactory;

@Component
@RequiredArgsConstructor
public class ExternalApiUtils {

    private final Log log = LogFactory.getLogger(this.getClass());
    private static final String CONTENT_TYPE = "Content-Type";
    private static final String TEMPLATE_LOG_REQUEST_START = "REST caller start. url : {} , requestHeader: {} , "
            + "requestBody: {} ";
    private static final String TEMPLATE_LOG_RESPONSE_END = "REST caller end.  url:{} , responseCode: {} , "
            + "responseHeader: {} , responseBody: {} ";
    private static final String TEMPLATE_LOG_RESPONSE_EXCEPTION = "REST caller end.  url:{} , responseCode: {} , "
            + "Exception : {} ";

    private final RestTemplate restTemplate;
    private final RestTemplate restTemplateCert;
    private final Gson gson;

    private static final ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());


    public Object restAPICallerSettradeHostLink(String url, HttpMethod httpMethod, HttpHeaders httpHeaders,
            Object requestObject, Class<?> requestType,
            Class<?> responseSuccess, Class<?> responseFailed) {

        ResponseEntity<String> restResponse = null;
        Object responseObject = null;

        String reqStr = null;

        if (StringUtils.isNotEmpty(url) && null != httpMethod) {

            if (httpHeaders == null) {
                httpHeaders = new HttpHeaders();
                httpHeaders.set(CONTENT_TYPE, CisConstants.CONTENT_TYPE_APPLICATION_JSON);
            }

            if (null != requestObject && null != requestType) {

                try {
                    reqStr = objectMapper.writeValueAsString(requestObject);
                } catch (JsonProcessingException ex) {
                    log.error("mapping json log request :error");
                    log.error(CommonUtil.convertStackToStingNotLimit(ex));
                    throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                            StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED, ex.toString());
                }
            }

            try {
                HttpEntity entity = new HttpEntity<>(requestObject, httpHeaders);
                log.info(TEMPLATE_LOG_REQUEST_START, url, httpHeaders, reqStr);
                restResponse = (ResponseEntity<String>) this.commonRestAPICaller(restTemplate, httpMethod, url, entity);
            } catch (Exception ex) {

                log.error(String.valueOf(ex));
                log.error(CommonUtil.convertStackToStingNotLimit(ex));
                if (ex instanceof HttpClientErrorException) {

                    HttpStatusCodeException httpStatusCodeException = (HttpStatusCodeException) ex;
                    int statusCode = httpStatusCodeException.getRawStatusCode();
                    log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, statusCode, ex.getMessage());

                    throw new CISServiceException(HttpStatus.CONFLICT, StatusResponseEnum.BAD_REQUEST, ex.toString());

                } else if (ex instanceof ResourceAccessException || ex instanceof ConnectException) {

                    log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, "", ex.getMessage());
                    throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                            StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED, ex.toString());

                } else if (ex instanceof HttpServerErrorException) { // 500

                    HttpStatusCodeException httpStatusCodeException = (HttpStatusCodeException) ex;
                    int statusCode = httpStatusCodeException.getRawStatusCode();
                    log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, statusCode, ex.getMessage());

                    throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                            StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED, ex.toString());

                } else {
                    throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                            StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED, ex.toString());
                }
            }
            responseObject = this.getCommonResponse(restResponse, responseSuccess, responseFailed);
        }
        return responseObject;
    }

    public Object restAPICaller(String url, HttpMethod httpMethod, HttpHeaders httpHeaders,
            Object requestObject, Class<?> requestType,
            Class<?> responseSuccess, Class<?> responseFailed) {

        ResponseEntity<String> restResponse = null;
        Object responseObject = null;

        String reqStr = null;

        if (StringUtils.isNotEmpty(url) && null != httpMethod) {

            if (httpHeaders == null) {
                httpHeaders = new HttpHeaders();
                httpHeaders.set(CONTENT_TYPE, CisConstants.CONTENT_TYPE_APPLICATION_JSON);
            }

            if (null != requestObject && null != requestType) {

                try {
                    reqStr = objectMapper.writeValueAsString(requestObject);
                } catch (JsonProcessingException ex) {
                    log.error("mapping json log request :error");
                    log.error(CommonUtil.convertStackToStingNotLimit(ex));
                    throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                            StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED, ex.toString());
                }
            }
            try {
                HttpEntity entity = new HttpEntity<>(requestObject, httpHeaders);
                log.info(TEMPLATE_LOG_REQUEST_START, url, httpHeaders, reqStr);
                restResponse = (ResponseEntity<String>) this.commonRestAPICaller(restTemplate, httpMethod, url, entity);
            } catch (Exception ex) {

                log.error("CommonRestAPICaller : {}", String.valueOf(ex));
                log.error(CommonUtil.convertStackToStingNotLimit(ex));
                if (ex instanceof HttpClientErrorException) {

                    HttpStatusCodeException httpStatusCodeException = (HttpStatusCodeException) ex;
                    int statusCode = httpStatusCodeException.getRawStatusCode();
                    log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, statusCode, ex.getMessage());

                    throw new CISServiceException(HttpStatus.CONFLICT, StatusResponseEnum.BAD_REQUEST, ex.toString());

                } else if (ex instanceof ResourceAccessException || ex instanceof ConnectException) {

                    log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, "", ex.getMessage());
                    throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                            StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED, ex.toString());

                } else if (ex instanceof HttpServerErrorException) { // 500

                    HttpStatusCodeException httpStatusCodeException = (HttpStatusCodeException) ex;
                    int statusCode = httpStatusCodeException.getRawStatusCode();
                    log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, statusCode, ex.getMessage());

                    throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                            StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED, ex.toString());

                } else {
                    throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                            StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED, ex.toString());
                }
            }
            responseObject = this.getCommonResponse(restResponse, responseSuccess, responseFailed);
        }
        return responseObject;

    }


    public Object restAPICallerCisCore(String url, HttpMethod httpMethod, HttpHeaders httpHeaders,
            Object requestObject, Class<?> requestType,
            Class<?> responseSuccess, Class<?> responseFailed) {

        ResponseEntity<String> restResponse = null;
        Object responseObject = null;

        String reqStr = null;

        if (StringUtils.isNotEmpty(url) && null != httpMethod) {

            if (httpHeaders == null) {
                httpHeaders = new HttpHeaders();
                httpHeaders.set(CONTENT_TYPE, CisConstants.CONTENT_TYPE_APPLICATION_JSON);
            }

            if (null != requestObject && null != requestType) {
//                reqStr = gsonConvert.toJson(requestObject, requestType);

                try {
                    reqStr = objectMapper.writeValueAsString(requestObject);
                } catch (JsonProcessingException ex) {
                    log.error("mapping json log request :error");
                    log.error(CommonUtil.convertStackToStingNotLimit(ex));
                    throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                            StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED, ex.toString());
                }
            }

            try {

                HttpEntity entity = new HttpEntity<>(requestObject, httpHeaders);
                log.info(TEMPLATE_LOG_REQUEST_START, url, httpHeaders, reqStr);
                restResponse = restTemplate.exchange(url, httpMethod, entity, String.class);
                log.info(TEMPLATE_LOG_RESPONSE_END, url, restResponse.getStatusCode(), restResponse.getHeaders(),
                        restResponse.getBody());

            } catch (Exception ex) {
                log.error(CommonUtil.convertStackToStingNotLimit(ex));
                if (ex instanceof HttpClientErrorException) { // 400
                    log.info(ex.getMessage());
                    HttpClientErrorException httpClientErrorException = (HttpClientErrorException) ex;
                    int statusCode = httpClientErrorException.getRawStatusCode();
                    log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, statusCode, ex.getMessage());

                    ResponseModel responseModel = JSONFasterXMLUtil.transformStringToVO(
                            httpClientErrorException.getResponseBodyAsString(), ResponseModel.class);

                    if (ObjectUtils.isEmpty(responseModel)) {

                        CISServiceException cisServiceException = CISServiceException.builder()
                                .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                                .statusResponse(StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED)
                                .errorStatusThirdParty(HttpStatus.valueOf(((HttpServerErrorException) ex).getStatusCode().value()))
                                .system("CIS-Core")
                                .message(ex.toString())
                                .build();
                        throw cisServiceException;
                    }

                    CISServiceException cisServiceException = CISServiceException.builder()
                            .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                            .statusResponse(StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED)
                            .statusResponseThirdParty(new StatusResponse(responseModel.getStatus().getCode(),
                                    responseModel.getStatus().getDescription()))
                            .errorStatusThirdParty(HttpStatus.valueOf(((HttpServerErrorException) ex).getStatusCode().value()))
                            .system("CIS-Core")
                            .message(ex.toString())
                            .build();
                    throw cisServiceException;

                } else if (ex instanceof HttpServerErrorException.GatewayTimeout) { // 504 gateway timeout

                    HttpStatusCodeException httpStatusCodeException = (HttpStatusCodeException) ex;
                    int statusCode = httpStatusCodeException.getRawStatusCode();
                    log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, statusCode, ex.getMessage());

                    // TODO : ASK SA : about handle this
                    CISServiceException cisServiceException = CISServiceException.builder()
                            .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                            .statusResponse(StatusResponseEnum.GET_CANNOT_CONNECT_TO_INTERNAL_MICROSERVICE)
                            .errorStatusThirdParty(HttpStatus.valueOf(((HttpServerErrorException) ex).getStatusCode().value()))
                            .system("CIS-Core")
                            .message(ex.toString())
                            .build();
                    throw cisServiceException;

                } else if (ex instanceof ResourceAccessException || ex instanceof ConnectException) { // cannot connect

                    // TODO : ASK SA : about handle this
                    // TODO : ASK SA : if error came from us
                    log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, "", ex.getMessage());

                    CISServiceException cisServiceException = CISServiceException.builder()
                            .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                            .statusResponse(StatusResponseEnum.GET_CANNOT_CONNECT_TO_INTERNAL_MICROSERVICE)
                            .system("CIS-Core")
                            .message(ex.toString())
                            .build();

                    throw cisServiceException;


                } else if (ex instanceof HttpServerErrorException) {

                    HttpServerErrorException httpServerErrorException = (HttpServerErrorException) ex;
                    int statusCode = httpServerErrorException.getRawStatusCode();
                    log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, statusCode, ex.getMessage());

                    ResponseModel responseModel = JSONFasterXMLUtil.transformStringToVO(
                            httpServerErrorException.getResponseBodyAsString(), ResponseModel.class);

                    if (ObjectUtils.isEmpty(responseModel)) { // TODO : check this null pointer exception

                        log.info("not have status mapping from cis-core");

                        // TODO : ASK SA : about handle this

                        CISServiceException cisServiceException = CISServiceException.builder()
                                .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                                .statusResponse(StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED)
                                .errorStatusThirdParty(HttpStatus.valueOf(((HttpServerErrorException) ex).getStatusCode().value()))
                                .system("CIS-Core")
                                .message(ex.toString())
                                .build();
                        throw cisServiceException;
                    }

                    CISServiceException cisServiceException = CISServiceException.builder()
                            .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                            .statusResponse(StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED)
                            .statusResponseThirdParty(new StatusResponse(responseModel.getStatus().getCode(),
                                    responseModel.getStatus().getDescription()))
                            .errorStatusThirdParty(HttpStatus.valueOf(((HttpServerErrorException) ex).getStatusCode().value()))
                            .system("CIS-Core")
                            .message(ex.toString())
                            .build();

                    throw cisServiceException;

                } else { // other exception
                    throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                            StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED, ex.toString());
                }
            }

            responseObject = this.getCommonResponse(restResponse, responseSuccess, responseFailed);
        }

        return responseObject;
    }

    private Object commonRestAPICaller(RestTemplate restTemplate, HttpMethod httpMethod, String url, HttpEntity entity)
            throws CISServiceException {

        ResponseEntity<String> restResponse = null;

        try {
            restResponse = restTemplate.exchange(url, httpMethod, entity, String.class);
            log.info(TEMPLATE_LOG_RESPONSE_END, url, restResponse.getStatusCode(), restResponse.getHeaders(),
                    restResponse.getBody());
        } catch (Exception ex) {

            log.error("CommonRestAPICaller : {}", String.valueOf(ex));
            log.error(CommonUtil.convertStackToStingNotLimit(ex));
            if (ex instanceof HttpClientErrorException) { // 400

                HttpStatusCodeException httpStatusCodeException = (HttpStatusCodeException) ex;
                int statusCode = httpStatusCodeException.getRawStatusCode();
                log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, statusCode, ex.getMessage());

                throw new CISServiceException(HttpStatus.CONFLICT, StatusResponseEnum.BAD_REQUEST, ex.toString());

            } else if (ex instanceof ResourceAccessException || ex instanceof ConnectException) {

                log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, "", ex.getMessage());
                throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                        StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED, ex.toString());

            } else if (ex instanceof HttpServerErrorException) { // 500

                HttpStatusCodeException httpStatusCodeException = (HttpStatusCodeException) ex;
                int statusCode = httpStatusCodeException.getRawStatusCode();
                log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, statusCode, ex.getMessage());

                throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                        StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED, ex.toString());

            } else {
                throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                        StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED, ex.toString());
            }
        }

        return restResponse;
    }

    private Object getCommonResponse(ResponseEntity<String> restResponse, Class<?> responseSuccess,
            Class<?> responseFailed) {

        Object response = null;

        try {

            if (HttpStatus.OK.equals(restResponse.getStatusCode()) || HttpStatus.ACCEPTED.equals(
                    restResponse.getStatusCode())) {

                response = null != restResponse.getBody()
                        ? objectMapper.readValue(restResponse.getBody(), responseSuccess)
                        : null;
            } else {

                response = null != restResponse.getBody()
                        ? objectMapper.readValue(restResponse.getBody(), responseFailed)
                        : null;
            }

        } catch (Exception ex) {

            log.error("{}", ex.getMessage());
            log.error(CommonUtil.convertStackToStingNotLimit(ex));
            throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                    StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED, ex.getLocalizedMessage());
        }

        return response;
    }

    private Object commonRestAPICallerCisCore(RestTemplate restTemplate, HttpMethod httpMethod, String url,
            HttpEntity entity) {

        ResponseEntity<String> restResponse = null;

        try {
            restResponse = restTemplate.exchange(url, httpMethod, entity, String.class);
            log.info(TEMPLATE_LOG_RESPONSE_END, url, restResponse.getStatusCode(), restResponse.getHeaders(),
                    restResponse.getBody());
        } catch (Exception ex) {
            log.error(CommonUtil.convertStackToStingNotLimit(ex));
            if (ex instanceof HttpClientErrorException) { // 400
                log.info(ex.getMessage());
                HttpClientErrorException httpClientErrorException = (HttpClientErrorException) ex;
                int statusCode = httpClientErrorException.getRawStatusCode();
                log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, statusCode, ex.getMessage());

                ResponseModel responseModel = JSONFasterXMLUtil.transformStringToVO(
                        httpClientErrorException.getResponseBodyAsString(), ResponseModel.class);

                if (ObjectUtils.isEmpty(responseModel)) {

                    CISServiceException cisServiceException = CISServiceException.builder()
                            .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                            .statusResponse(StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED)
                            .errorStatusThirdParty(HttpStatus.valueOf(((HttpServerErrorException) ex).getStatusCode().value()))
                            .system("CIS-Core")
                            .build();
                    throw cisServiceException;
                }

                CISServiceException cisServiceException = CISServiceException.builder()
                        .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                        .statusResponse(StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED)
                        .statusResponseThirdParty(new StatusResponse(responseModel.getStatus().getCode(),
                                responseModel.getStatus().getDescription()))
                        .errorStatusThirdParty(HttpStatus.valueOf(((HttpServerErrorException) ex).getStatusCode().value()))
                        .system("CIS-Core")
                        .build();
                throw cisServiceException;

            } else if (ex instanceof HttpServerErrorException.GatewayTimeout) { // 504 gateway timeout

                HttpStatusCodeException httpStatusCodeException = (HttpStatusCodeException) ex;
                int statusCode = httpStatusCodeException.getRawStatusCode();
                log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, statusCode, ex.getMessage());

                // TODO : ASK SA : about handle this
                CISServiceException cisServiceException = CISServiceException.builder()
                        .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                        .statusResponse(StatusResponseEnum.GET_CANNOT_CONNECT_TO_INTERNAL_MICROSERVICE)
                        .errorStatusThirdParty(HttpStatus.valueOf(((HttpServerErrorException) ex).getStatusCode().value()))
                        .system("CIS-Core")
                        .build();
                throw cisServiceException;

            } else if (ex instanceof ResourceAccessException || ex instanceof ConnectException) { // cannot connect

                // TODO : ASK SA : about handle this
                // TODO : ASK SA : if error came from us
                log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, "", ex.getMessage());

                CISServiceException cisServiceException = CISServiceException.builder()
                        .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                        .statusResponse(StatusResponseEnum.GET_CANNOT_CONNECT_TO_INTERNAL_MICROSERVICE)
                        .system("CIS-Core")
                        .build();

                throw cisServiceException;


            } else if (ex instanceof HttpServerErrorException) {

                HttpServerErrorException httpServerErrorException = (HttpServerErrorException) ex;
                int statusCode = httpServerErrorException.getRawStatusCode();
                log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, statusCode, ex.getMessage());

                ResponseModel responseModel = JSONFasterXMLUtil.transformStringToVO(
                        httpServerErrorException.getResponseBodyAsString(), ResponseModel.class);

                if (ObjectUtils.isEmpty(responseModel)) { // TODO : check this null pointer exception

                    log.info("not have status mapping from cis-core");

                    // TODO : ASK SA : about handle this

                    CISServiceException cisServiceException = CISServiceException.builder()
                            .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                            .statusResponse(StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED)
                            .errorStatusThirdParty(HttpStatus.valueOf(((HttpServerErrorException) ex).getStatusCode().value()))
                            .system("CIS-Core")
                            .build();
                    throw cisServiceException;
                }

                CISServiceException cisServiceException = CISServiceException.builder()
                        .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                        .statusResponse(StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED)
                        .statusResponseThirdParty(new StatusResponse(responseModel.getStatus().getCode(),
                                responseModel.getStatus().getDescription()))
                        .errorStatusThirdParty(HttpStatus.valueOf(((HttpServerErrorException) ex).getStatusCode().value()))
                        .system("CIS-Core")
                        .build();

                throw cisServiceException;

            } else { // other exception
                throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                        StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED);
            }
        }
        return restResponse;
    }


    public <T> ResponseEntity<T> restAPICallerCisKyc(String url, HttpMethod httpMethod, HttpHeaders httpHeaders,
            Object requestObject, Class<T> tClass) {

        String reqStr;
        ResponseEntity<T> responseEntity = null;

        if (StringUtils.isNotEmpty(url) && null != httpMethod) {

            if (httpHeaders == null) {
                httpHeaders = new HttpHeaders();
                httpHeaders.set(CONTENT_TYPE, CisConstants.CONTENT_TYPE_APPLICATION_JSON);
            }

            try {
                reqStr = objectMapper.writeValueAsString(requestObject);
            } catch (JsonProcessingException ex) {
                log.error("mapping json log request :error");
                log.error(CommonUtil.convertStackToStingNotLimit(ex));
                throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                        StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED, "mapping json log request :error");
            }

            try {

                HttpEntity entity = new HttpEntity<>(requestObject, httpHeaders);
                log.info(TEMPLATE_LOG_REQUEST_START, url, httpHeaders, reqStr);
                responseEntity = restTemplate.exchange(url, httpMethod, entity, tClass);
                log.info(TEMPLATE_LOG_RESPONSE_END, url, responseEntity.getStatusCode(), responseEntity.getHeaders(),
                        responseEntity.getBody());

            } catch (Exception ex) {
                log.error(CommonUtil.convertStackToStingNotLimit(ex));

                if (ex instanceof ResourceAccessException || ex instanceof ConnectException) {

                    CISServiceException cisEx = CISServiceException.builder()
                            .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                            .statusResponse(StatusResponseEnum.KYC_API_TIMEOUT)
                            .system("CIS-KYC")
                            .message(ex.getMessage())
                            .build();

                    throw cisEx;

                } else if (ex instanceof HttpServerErrorException.BadGateway
                        || ex instanceof HttpServerErrorException.ServiceUnavailable
                        || ex instanceof HttpServerErrorException.GatewayTimeout
                        || ex instanceof HttpClientErrorException.NotFound) {

                    CISServiceException cisEx = CISServiceException.builder()
                            .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                            .statusResponse(StatusResponseEnum.CANNOT_CONNECT_TO_KYC_API)
                            .system("CIS-KYC")
                            .message(ex.getMessage())
                            .build();

                    throw cisEx;

                } else {
                    throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                            StatusResponseEnum.RECEIVED_ERROR_FROM_KYC_API, ex.getMessage());
                }
            }
        }
        return responseEntity;
    }


    private Object commonRestAPICallerCisKyc(RestTemplate restTemplate, HttpMethod httpMethod, String url,
            HttpEntity entity) {

        ResponseEntity<String> restResponse = null;

        try {
            log.info("REST caller start. url : {} , httpMethod: {} entity: {}", url, httpMethod, entity);
            restResponse = restTemplate.exchange(url, httpMethod, entity, String.class);
            log.info(TEMPLATE_LOG_RESPONSE_END, url, restResponse.getStatusCode(), restResponse.getHeaders(),
                    restResponse.getBody());
        } catch (Exception ex) {
            log.error(CommonUtil.convertStackToStingNotLimit(ex));
            if (ex instanceof HttpClientErrorException) { // 400
                log.info(ex.getMessage());
                HttpClientErrorException httpClientErrorException = (HttpClientErrorException) ex;
                int statusCode = httpClientErrorException.getRawStatusCode();
                log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, statusCode, ex.getMessage());

                ResponseModel responseModel = JSONFasterXMLUtil.transformStringToVO(
                        httpClientErrorException.getResponseBodyAsString(), ResponseModel.class);

                if (ObjectUtils.isEmpty(responseModel)) {

                    CISServiceException cisServiceException = CISServiceException.builder()
                            .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                            .statusResponse(StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED)
                            .errorStatusThirdParty(HttpStatus.valueOf(((HttpServerErrorException) ex).getStatusCode().value())).build();
                    throw cisServiceException;
                }

                CISServiceException cisServiceException = CISServiceException.builder()
                        .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                        .statusResponse(StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED)
                        .statusResponseThirdParty(new StatusResponse(responseModel.getStatus().getCode(),
                                responseModel.getStatus().getDescription()))
                        .errorStatusThirdParty(HttpStatus.valueOf(((HttpServerErrorException) ex).getStatusCode().value())).build();
                throw cisServiceException;

            } else if (ex instanceof HttpServerErrorException.GatewayTimeout) { // 504 gateway timeout

                HttpStatusCodeException httpStatusCodeException = (HttpStatusCodeException) ex;
                int statusCode = httpStatusCodeException.getRawStatusCode();
                log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, statusCode, ex.getMessage());

                // TODO : ASK SA : about handle this
                CISServiceException cisServiceException = CISServiceException.builder()
                        .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                        .statusResponse(StatusResponseEnum.GET_CANNOT_CONNECT_TO_INTERNAL_MICROSERVICE)
                        .errorStatusThirdParty(HttpStatus.valueOf(((HttpServerErrorException) ex).getStatusCode().value()))
                        .build();
                throw cisServiceException;

            } else if (ex instanceof ResourceAccessException || ex instanceof ConnectException) { // cannot connect

                // TODO : ASK SA : about handle this
                // TODO : ASK SA : if error came from us
                log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, "", ex.getMessage());
//                throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR, StatusResponseEnum
//                .GET_CANNOT_CONNECT_TO_INTERNAL_MICROSERVICE,new StatusResponse(4001, "Business error : CIS
//                Microservice error"));
                throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                        StatusResponseEnum.GET_CANNOT_CONNECT_TO_INTERNAL_MICROSERVICE);


            } else if (ex instanceof HttpServerErrorException) {

                HttpServerErrorException httpServerErrorException = (HttpServerErrorException) ex;
                int statusCode = httpServerErrorException.getRawStatusCode();
                log.error(TEMPLATE_LOG_RESPONSE_EXCEPTION, url, statusCode, ex.getMessage());

                ResponseModel responseModel = JSONFasterXMLUtil.transformStringToVO(
                        httpServerErrorException.getResponseBodyAsString(), ResponseModel.class);

                if (ObjectUtils.isEmpty(responseModel)) { // TODO : check this null pointer exception

                    log.info("not have status mapping from cis-core");

                    // TODO : ASK SA : about handle this

                    CISServiceException cisServiceException = CISServiceException.builder()
                            .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                            .statusResponse(StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED)
                            .errorStatusThirdParty(HttpStatus.valueOf(((HttpServerErrorException) ex).getStatusCode().value()))
//                            .statusResponseThirdParty( new StatusResponse(responseModel.getStatus().getCode(),
//                            responseModel.getStatus().getDescription()))
                            .build();
                    throw cisServiceException;
                }

                CISServiceException cisServiceException = CISServiceException.builder()
                        .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                        .statusResponse(StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED)
                        .statusResponseThirdParty(new StatusResponse(responseModel.getStatus().getCode(),
                                responseModel.getStatus().getDescription()))
                        .errorStatusThirdParty(HttpStatus.valueOf(((HttpServerErrorException) ex).getStatusCode().value())).build();
                throw cisServiceException;

            } else { // other exception
                throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                        StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED);
            }
        }
        return restResponse;
    }


    public <T> ResponseEntity<T> restAPICallerCdb(String url, HttpMethod httpMethod, HttpHeaders httpHeaders,
            Object requestObject, Class<T> tClass) {

        String reqStr;
        ResponseEntity<T> responseEntity = null;

        if (StringUtils.isNotEmpty(url) && null != httpMethod) {

            if (httpHeaders == null) {
                httpHeaders = new HttpHeaders();
                httpHeaders.set(CONTENT_TYPE, CisConstants.CONTENT_TYPE_APPLICATION_JSON);
            }

            try {
                reqStr = objectMapper.writeValueAsString(requestObject);
            } catch (JsonProcessingException ex) {
                log.error("mapping json log request :error");
                log.error(CommonUtil.convertStackToStingNotLimit(ex));
                throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                        StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED, "mapping json log request :error");
            }

            try {

                HttpEntity entity = new HttpEntity<>(requestObject, httpHeaders);
                log.info(TEMPLATE_LOG_REQUEST_START, url, httpHeaders, reqStr);
                responseEntity = restTemplateCert.exchange(url, httpMethod, entity, tClass);
                log.info(TEMPLATE_LOG_RESPONSE_END, url, responseEntity.getStatusCode(), responseEntity.getHeaders(),
                        responseEntity.getBody());

            } catch (Exception ex) {

                log.error(CommonUtil.convertStackToStingNotLimit(ex));

                if (ex instanceof ResourceAccessException || ex instanceof ConnectException) {

                    CISServiceException cisEx = CISServiceException.builder()
                            .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                            .statusResponse(StatusResponseEnum.CDB_API_TIMEOUT)
                            .message(ex.getMessage())
                            .system("CDB-MS")
                            .build();

                    throw cisEx;

                } else if (ex instanceof HttpServerErrorException.BadGateway
                        || ex instanceof HttpServerErrorException.ServiceUnavailable
                        || ex instanceof HttpServerErrorException.GatewayTimeout
                        || ex instanceof HttpServerErrorException.InternalServerError) {

                    CISServiceException cisEx = CISServiceException.builder()
                            .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                            .statusResponse(StatusResponseEnum.CANNOT_CONNECT_TO_CDB_API)
                            .message(ex.getMessage())
                            .system("CDB-MS")
                            .build();
                    throw cisEx;

                } else {
                    log.error("CDB Common error");
                    if (ex instanceof HttpClientErrorException) {
                        HttpClientErrorException clientEx = (HttpClientErrorException) ex;
                        log.error("CDB HttpClientErrorException code {}", clientEx.getStatusCode());
                        log.error("CDB HttpClientErrorException response {}", clientEx.getResponseBodyAsString());
                        //objectMapper.re
                        if (clientEx.getStatusCode().value() == 422 && StringUtils.isNotEmpty(
                                clientEx.getResponseBodyAsString())) {
                            try {
                                CDBErrorRespionse response = objectMapper.readValue(clientEx.getResponseBodyAsString(),
                                        new TypeReference<>() {
                                        });
                                if (response != null) {
                                    log.info("response code {}", response.getCode());
                                    log.info("response message {}", response.getMessage());
                                    if ("E0028".equalsIgnoreCase(response.getCode())) {
                                        throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                                                StatusResponseEnum.SKIP);
                                    }
                                }
                            } catch (CISServiceException cs) {
                                throw cs;
                            } catch (Exception e) {
                                log.error(CommonUtil.convertStackToStingNotLimit(e));
                            }
                        }
                    }

                    throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                            StatusResponseEnum.RECEIVED_ERROR_FROM_CDB_API);
                }  // todo : chekc if not ex from cdb
            }
        }

        return responseEntity;
    }

    public ResponseEntity<CdbResponseEntity> restAPICallerKycCdb(String url, HttpMethod httpMethod,
            HttpHeaders httpHeaders, Object requestObject, Class<?> requestType) {
        ResponseEntity<CdbResponseEntity> responseEntity = null;
        if (StringUtils.isNotEmpty(url) && null != httpMethod) {
            if (httpHeaders == null) {
                httpHeaders = new HttpHeaders();
                httpHeaders.set(CONTENT_TYPE, CisConstants.CONTENT_TYPE_APPLICATION_JSON);
            }
            String reqStr = null;
            if (null != requestObject && null != requestType) {
                reqStr = gson.toJson(requestObject, requestType);
            }
            HttpEntity<Object> entity = new HttpEntity<>(requestObject, httpHeaders);
            log.info(TEMPLATE_LOG_REQUEST_START, url, httpHeaders, reqStr);
            responseEntity = this.commonRestAPICallerCdb(httpMethod, url, entity);
        }

        return responseEntity;
    }

    private ResponseEntity<CdbResponseEntity> commonRestAPICallerCdb(HttpMethod httpMethod, String url,
            HttpEntity<Object> entity) {
        ResponseEntity<CdbResponseEntity> restResponse;

        try {

            restResponse = restTemplateCert.exchange(url, httpMethod, entity, CdbResponseEntity.class);
            log.info(TEMPLATE_LOG_RESPONSE_END, url, restResponse.getStatusCode(), restResponse.getHeaders(),
                    restResponse.getBody());

        } catch (Exception ex) {

            log.error(CommonUtil.convertStackToStingNotLimit(ex));

            if (ex instanceof ResourceAccessException || ex instanceof ConnectException) {

                CISServiceException cisEx = CISServiceException.builder()
                        .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                        .statusResponse(StatusResponseEnum.CDB_API_TIMEOUT)
                        .message(ex.getMessage())
                        .system("CDB-MS")
                        .build();

                throw cisEx;

            } else if (ex instanceof HttpServerErrorException.BadGateway
                    || ex instanceof HttpServerErrorException.ServiceUnavailable
                    || ex instanceof HttpServerErrorException.GatewayTimeout
                    || ex instanceof HttpServerErrorException.InternalServerError) {

                CISServiceException cisEx = CISServiceException.builder()
                        .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                        .statusResponse(StatusResponseEnum.CANNOT_CONNECT_TO_CDB_API)
                        .message(ex.getMessage())
                        .system("CDB-MS")
                        .build();
                throw cisEx;

            } else {
                throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                        StatusResponseEnum.RECEIVED_ERROR_FROM_CDB_API);
            }  // todo : chekc if not ex from cdb
        }

        return restResponse;
    }


    public String generateUriString(String url, Map<String, String> params) { // have problem with thai lang

        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        for (Map.Entry<String, String> entry : params.entrySet()) {
            builder.queryParam(entry.getKey(), entry.getValue());
        }

        return builder.toUriString();
    }

    public String getUrlAndQueryParam(String url, Map<String, String> params) { // not have problem with thai lang

        StringBuilder urlAndQueryParam = new StringBuilder();
        urlAndQueryParam.append(url);

        boolean checkAllNullInParam = params.values()
                .stream()
                .allMatch(Objects::isNull);

        if (!ObjectUtils.isEmpty(params) && !checkAllNullInParam) {
            urlAndQueryParam.append("?");
            Iterator<Map.Entry<String, String>> iterator = params.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, String> entry = iterator.next();

                if (StringUtils.isNotEmpty(entry.getValue())) {
                    urlAndQueryParam.append(entry.getKey());
                    urlAndQueryParam.append("=");
                    urlAndQueryParam.append(
                            StringUtils.isEmpty(entry.getValue()) ? StringUtils.EMPTY : entry.getValue());

                    if (iterator.hasNext()) {
                        urlAndQueryParam.append("&");
                    }
                }
            }
        }
        return urlAndQueryParam.toString();
    }

    public HttpHeaders restAPICallerSettradeGetHeader(String url, HttpMethod httpMethod, HttpHeaders httpHeaders) {

        ResponseEntity<String> restResponse = null;

        if (StringUtils.isNotEmpty(url) && null != httpMethod) {
            HttpEntity entity = new HttpEntity<>(httpHeaders);
            log.info(TEMPLATE_LOG_REQUEST_START, url, httpHeaders, "");
            restResponse = (ResponseEntity<String>) this.restAPICallerSettrade(restTemplate, httpMethod, url, entity);
        }

        return Objects.requireNonNull(restResponse).getHeaders();
    }

    public Object restAPICallerSettradeGetBody(String url, HttpMethod httpMethod, HttpHeaders httpHeaders) {

        ResponseEntity<String> restResponse = null;

        if (StringUtils.isNotEmpty(url) && null != httpMethod) {
            HttpEntity entity = new HttpEntity<>(httpHeaders);
            log.info(TEMPLATE_LOG_REQUEST_START, url, httpHeaders, "");
            restResponse = (ResponseEntity<String>) this.restAPICallerSettrade(restTemplate, httpMethod, url, entity);
        }

        return Objects.requireNonNull(restResponse).getBody();
    }


    public Object restAPICallerSettrade(String url, HttpMethod httpMethod, HttpHeaders httpHeaders) {

        ResponseEntity<String> restResponse = null;

        if (StringUtils.isNotEmpty(url) && null != httpMethod) {
            HttpEntity entity = new HttpEntity<>(httpHeaders);
            log.info(TEMPLATE_LOG_REQUEST_START, url, httpHeaders, "");
            restResponse = (ResponseEntity<String>) this.restAPICallerSettrade(restTemplate, httpMethod, url, entity);
        }

        return Objects.requireNonNull(restResponse).getBody();
    }


    private Object restAPICallerSettrade(RestTemplate restTemplate, HttpMethod httpMethod, String url,
            HttpEntity entity) throws CISServiceException {

        ResponseEntity<String> restResponse = null;

        try {
            restResponse = restTemplate.exchange(url, httpMethod, entity, String.class);
            log.info(TEMPLATE_LOG_RESPONSE_END, url, restResponse.getStatusCode(), restResponse.getHeaders(),
                    restResponse.getBody());
        } catch (Exception ex) {
            log.error("Got Unknown error when call function: {}", ex);
            log.error(CommonUtil.convertStackToStingNotLimit(ex));
            throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                    StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED,
                    List.of(ErrorModel.builder().message("Got Unknown error when call function to SETTRADE").build()));
        }

        return restResponse;
    }

    public <T> ResponseEntity<T> restAPICallerEnterpriseApi(String url, HttpMethod httpMethod, HttpHeaders httpHeaders,
            Object requestObject, Class<T> tClass) {

        String reqStr;
        ResponseEntity<T> responseEntity = null;

        if (StringUtils.isNotEmpty(url) && null != httpMethod) {

            if (httpHeaders == null) {
                httpHeaders = new HttpHeaders();
                httpHeaders.set(CONTENT_TYPE, CisConstants.CONTENT_TYPE_APPLICATION_JSON);
            }

            try {
                reqStr = objectMapper.writeValueAsString(requestObject);
            } catch (JsonProcessingException ex) {
                log.error("mapping json log request :error");
                log.error(CommonUtil.convertStackToStingNotLimit(ex));
                throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                        StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED, "mapping json log request :error");
            }

            try {

                HttpEntity entity = new HttpEntity<>(requestObject, httpHeaders);
                log.info(TEMPLATE_LOG_REQUEST_START, url, httpHeaders, reqStr);
                responseEntity = restTemplateCert.exchange(url, httpMethod, entity, tClass);
                log.info(TEMPLATE_LOG_RESPONSE_END, url, responseEntity.getStatusCode(), responseEntity.getHeaders(),
                        responseEntity.getBody());

            } catch (Exception ex) {
                log.error(CommonUtil.convertStackToStingNotLimit(ex));

                if (ex instanceof ResourceAccessException || ex instanceof ConnectException) {

                    CISServiceException cisEx = CISServiceException.builder()
                            .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                            .statusResponse(StatusResponseEnum.EAPI_API_TIMEOUT)
                            .system("CIS-EAPI")
                            .message(ex.getMessage())
                            .build();

                    throw cisEx;

                } else if (ex instanceof HttpServerErrorException.BadGateway
                        || ex instanceof HttpServerErrorException.ServiceUnavailable
                        || ex instanceof HttpServerErrorException.GatewayTimeout
                        || ex instanceof HttpClientErrorException.NotFound) {

                    CISServiceException cisEx = CISServiceException.builder()
                            .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                            .statusResponse(StatusResponseEnum.CANNOT_CONNECT_TO_EAPI_API)
                            .system("CIS-EAPI")
                            .message(ex.getMessage())
                            .build();

                    throw cisEx;

                } else {
                    throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                            StatusResponseEnum.RECEIVED_ERROR_FROM_EAPI_API, ex.getMessage());
                }
            }
        }
        return responseEntity;
    }

    public <T> ResponseEntity<T> restAPICallerBox2Api(String url, HttpMethod httpMethod, HttpHeaders httpHeaders,
            Object requestObject, Class<T> tClass) {

        String reqStr;
        ResponseEntity<T> responseEntity = null;

        if (StringUtils.isNotEmpty(url) && null != httpMethod) {

            if (httpHeaders == null) {
                httpHeaders = new HttpHeaders();
                httpHeaders.set(CONTENT_TYPE, CisConstants.CONTENT_TYPE_APPLICATION_JSON);
            }

            try {
                reqStr = objectMapper.writeValueAsString(requestObject);
            } catch (JsonProcessingException ex) {
                log.error("mapping json log request :error");
                log.error(CommonUtil.convertStackToStingNotLimit(ex));
                throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                        StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED, "mapping json log request :error");
            }

            try {

                HttpEntity entity = new HttpEntity<>(requestObject, httpHeaders);
                log.info(TEMPLATE_LOG_REQUEST_START, url, httpHeaders, reqStr);
                responseEntity = restTemplateCert.exchange(url, httpMethod, entity, tClass);
                log.info(TEMPLATE_LOG_RESPONSE_END, url, responseEntity.getStatusCode(), responseEntity.getHeaders(),
                        responseEntity.getBody());
            } catch (HttpClientErrorException ex) {
                String responseBody = ex.getResponseBodyAsString();
                throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                        StatusResponseEnum.RECEIVED_ERROR_FROM_BOX2_API, responseBody);
            } catch (Exception ex) {
                log.error(CommonUtil.convertStackToStingNotLimit(ex));

                if (ex instanceof ResourceAccessException || ex instanceof ConnectException) {

                    CISServiceException cisEx = CISServiceException.builder()
                            .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                            .statusResponse(StatusResponseEnum.BOX2_API_TIMEOUT)
                            .system("CIS-EAPI")
                            .message(ex.getMessage())
                            .build();

                    throw cisEx;

                } else if (ex instanceof HttpServerErrorException.BadGateway
                        || ex instanceof HttpServerErrorException.ServiceUnavailable
                        || ex instanceof HttpServerErrorException.GatewayTimeout
                        || ex instanceof HttpClientErrorException.NotFound) {

                    CISServiceException cisEx = CISServiceException.builder()
                            .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                            .statusResponse(StatusResponseEnum.CANNOT_CONNECT_TO_BOX2_API)
                            .system("CIS-EAPI")
                            .message(ex.getMessage())
                            .build();

                    throw cisEx;

                } else {
                    throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                            StatusResponseEnum.RECEIVED_ERROR_FROM_BOX2_API, ex.getMessage());
                }
            }
        }
        return responseEntity;
    }

}
