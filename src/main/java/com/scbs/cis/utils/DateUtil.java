package com.scbs.cis.utils;

import com.scbs.cis.constants.StatusResponseEnum;
import com.scbs.cis.exception.CISServiceException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

@Component
public class DateUtil {

    public static final String DATE_YYYY_MM_DD = "yyyy-MM-dd";
    public static final String DATE_DD_MM_YYYY = "dd/MM/yyyy";
    public static final String DATE_FORMAT_PATTERN_FOR_SBA_MS = "yyyyMMdd";

    public static final String FILE_NAME_DATE_TIME = "yyyyMMddHHmmssSSS";
    public static final String DATE_DISPLAY_ON_EMAIL = "dd/MM/yyyy HH:mm";

    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final Locale DEFAULT_LOCALE = new Locale("en", "EN");

    public static LocalDate getDateFromTimeStamp(Timestamp timestamp) {
        if (ObjectUtils.isNotEmpty(timestamp)) {
            return timestamp.toLocalDateTime().toLocalDate();
        }
        return null;
    }

    public static LocalDateTime getDateTimeFromTimeStamp(Timestamp timestamp) {
        if (ObjectUtils.isNotEmpty(timestamp)) {

            LocalDateTime localDateTime = timestamp.toLocalDateTime();
            return localDateTime;
        }
        return null;
    }


    public static LocalDateTime convertStringToLocalDateTime(String dateTime) {

        if (!ValidateUtil.isEmptyOrNull(dateTime)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return LocalDateTime.parse(dateTime, formatter);
        } else {
            return null;
        }

    }

    public static LocalDate convertStringToLocalDate(String dateTime) {
        if (!ValidateUtil.isEmptyOrNull(dateTime)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return LocalDate.parse(dateTime, formatter);
        } else {
            return null;
        }
    }

    public static String convertLocalDateTimeToString(LocalDateTime dateTime, String format) {

        try {

            if (!ValidateUtil.isEmptyOrNull(dateTime)) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
                return dateTime.format(formatter);
            } else {
                return null;
            }

        } catch (Exception ex) {
            throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                    StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED, "cannot convert dateTime to string");
        }

    }

    public static String convertLocalDateToString(LocalDate localDate, String format) {
        if (!ValidateUtil.isEmptyOrNull(localDate)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            return localDate.format(formatter);
        } else {
            return null;
        }
    }

    public static String convertLocalDateToStringSupportEmptyString(LocalDate localDate, String format) {
        if (!ValidateUtil.isEmptyOrNull(localDate)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            return localDate.format(formatter);
        } else {
            return "";
        }
    }


    public static Date convertZoneDateTimeToDate(ZonedDateTime dateToConvert) {
        if (!ValidateUtil.isEmptyOrNull(dateToConvert)) {
            Date dateResult = Date.from(dateToConvert.toInstant());
            return dateResult;
        } else {
            return null;
        }
    }


    public static String getSystemDateTime() {
        String patternDate = "dd/MM/yyyy";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(patternDate);
        String date = simpleDateFormat.format(new Date());

        String patternTime = "HH:mm";
        SimpleDateFormat simpleTimeFormat = new SimpleDateFormat(patternTime);
        String time = simpleTimeFormat.format(new Date());
        String dateTimeField = date + " เวลา " + time;

        return dateTimeField;
    }

    public static String getSystemDateTimeNoText() {
        String patternDate = "dd/MM/yyyy";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(patternDate);
        String date = simpleDateFormat.format(new Date());

        String patternTime = "HH:mm";
        SimpleDateFormat simpleTimeFormat = new SimpleDateFormat(patternTime);
        String time = simpleTimeFormat.format(new Date());
        String dateTimeField = date + " " + time;

        return dateTimeField;
    }

    public String verifyDate(String input) {
        SimpleDateFormat sdf = new java.text.SimpleDateFormat(DATE_YYYY_MM_DD);

        if (input != null) {
            try {
                java.util.Date ret = sdf.parse(input.trim());
                if (sdf.format(ret).equals(input.trim())) {
                    return input;
                }
            } catch (ParseException e) {
                return null;
            }
        }
        return null;
    }


    public static String diffDate(int date, String pattern) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DATE, date);
        return formatDateToString(cal.getTime(), pattern);
    }

    public static String formatDateToString(Date date, String pattern) {
        SimpleDateFormat format;
        if (date != null && StringUtils.isNotEmpty(pattern)) {
            format = new SimpleDateFormat(pattern, DEFAULT_LOCALE);
            return format.format(date);
        } else {
            return null;
        }
    }

    public static String formatDateToString(String dateStr, String pattern) {
        try {
            DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            Date date = (Date) formatter.parse(dateStr);
            SimpleDateFormat newFormat = new SimpleDateFormat(pattern);
            return newFormat.format(date);
        } catch (Exception ex) {
        }

        return "";
    }

}