package com.scbs.cis.utils;

import static com.scbs.cis.constants.CisConstants.ADMIN_ACTIVATION_PREFIX;
import static com.scbs.cis.constants.CisConstants.PRODUCT_ACTIVATION_PREFIX;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.base.CaseFormat;
import com.scbs.cis.constants.CisConstants;
import com.scbs.cis.constants.StatusResponseEnum;
import com.scbs.cis.exception.CISServiceException;
import com.scbs.cis.model.StackTrack;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.security.SecureRandom;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class CommonUtil {


    static ObjectMapper mapper = new ObjectMapper();

    public static boolean validatePattern(String input, String regex) {
        if (StringUtils.isNotEmpty(regex)) {
            Pattern pattern = Pattern.compile(regex);
            if (pattern.matcher(input).find()) {
                return true;
            }
        }
        return false;
    }

    public static String generateUUID() {
        UUID uuid = UUID.randomUUID();
        String uuidAsString = uuid.toString();
        return uuidAsString;
    }

    public static String systemDefault(String value) {
        if (null == value || value.isEmpty()) {
            return CisConstants.SYSTEM;
        } else {
            return value;
        }
    }

    public static StackTrack convertStacktrack(Throwable throwable) {
        if (throwable == null) {
            return null;
        }
        StackTrack stack = new StackTrack();
        stack.setMessage(throwable.getMessage());
        StackTraceElement[] elements = throwable.getStackTrace();
        List<String> stacks = new ArrayList<>();
        StackTraceElement element;
        for (int i = 0; i < elements.length && i < 20; i++) {
            element = elements[i];
            stacks.add(
                    String.format("%s:%d %s", element.getFileName(), element.getLineNumber(), element.getMethodName()));
        }
        stack.setStacks(stacks);
        return stack;
    }

    public static String convertStackToSting(Throwable throwable) {
        StackTrack stackTrack = convertStacktrack(throwable);
        if (stackTrack == null) {
            return "";
        }
        ObjectMapper objectMapper = JSONFasterXMLUtil.getMapper();
        try {
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(stackTrack);
        } catch (Exception e) {
            StringWriter writer = new StringWriter();
            PrintWriter pw = new PrintWriter(writer, true);
            throwable.printStackTrace(pw);
            String exception = writer.toString();
            if (exception.length() > 500) {
                exception = exception.substring(0, 500);
            }
            return exception;
        }

    }

    public static <T> String getStringValue(T value) {

        if (value != null) {
            return String.valueOf(value);
        }

        return "";
    }

    public static <T> String getString(T value) {

        if (value != null) {
            return String.valueOf(value);
        }

        return "";
    }


    public static String convertStackToStingNotLimit(Throwable throwable) {
        StackTrack stackTrack = convertStacktrack(throwable);
        if (stackTrack == null) {
            return "";
        }
        ObjectMapper objectMapper = JSONFasterXMLUtil.getMapper();
        try {
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(stackTrack);
        } catch (Exception e) {
            StringWriter writer = new StringWriter();
            PrintWriter pw = new PrintWriter(writer, true);
            throwable.printStackTrace(pw);
            String exception = writer.toString();
            return exception;
        }

    }

    public static String numberFormat(BigDecimal var) {
        DecimalFormat df = new DecimalFormat("#,##0.00");
        df.setMaximumFractionDigits(2);
        return df.format(var);
    }

    public static String numberFormatNoDot(BigDecimal var) {
        DecimalFormat df = new DecimalFormat("#,##0");
        df.setMaximumFractionDigits(2);
        return df.format(var);
    }

    public static <T> boolean isDiff(T t, T k) {

        if (t == null && k == null) {
            return false;
        }

        if (t != null && !t.equals(k)) {
            return true;
        }

        return false;
    }

    public static <T> T setValueIfDiff(T t, T k) {

        if (isDiff(t, k)) {
            return t;
        }

        return null;

    }

    public static <T> T getFirstIfHaveValue(T t, T k) {

        if (t != null) {
            return t;
        }

        return k;
    }

    public static <T> T merge(T t, T k, T obj) throws CISServiceException {

        Field[] allFields = t.getClass().getDeclaredFields();
        Arrays.stream(allFields).forEach(
                field -> {
                    try {
                        Method setMethodObj = obj.getClass()
                                .getMethod("set" + CaseFormat.LOWER_CAMEL.to(CaseFormat.UPPER_CAMEL, field.getName()),
                                        field.getType());
                        Method getTobe = t.getClass()
                                .getMethod("get" + CaseFormat.LOWER_CAMEL.to(CaseFormat.UPPER_CAMEL, field.getName()));
                        Method getExist = t.getClass()
                                .getMethod("get" + CaseFormat.LOWER_CAMEL.to(CaseFormat.UPPER_CAMEL, field.getName()));
                        setMethodObj.invoke(obj, getFirstIfHaveValue(getTobe.invoke(t), getExist.invoke(k)));

                    } catch (Exception e) {
                        throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                                StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED);
                    }
                }
        );
        return obj;
    }

    public static String joinWithSeparatorIfPreviousNotEmpty(String separator, String previous, String value) {

        if (separator == null) {
            separator = "";
        }
        if (previous == null) {
            previous = "";
        }
        if (value == null) {
            value = "";
        }

        if (StringUtils.isNotEmpty(value)) {
            previous = StringUtils.join(previous,
                    StringUtils.isNotEmpty(previous) ? StringUtils.join(separator, value) : value);
        }

        return previous;
    }

    public static <T> T mergeNodes(JsonNode rootOld, JsonNode rootNew, String path, Class convertClass) {
        JsonNode oldNode = rootOld.at(path);
        JsonNode newNode = rootNew.at(path);
        JsonNode result = mergeNodes(oldNode, newNode);
        if (result != null && !result.isMissingNode() && !result.isNull()) {
            return mapper.convertValue(result, mapper.constructType(convertClass));
        } else {
            return null;
        }
    }


    public static JsonNode mergeNodes(JsonNode oldNode, JsonNode newNode) {
        if (newNode == null || newNode.isMissingNode()) {
            return oldNode;
        } else if (oldNode == null || oldNode.isMissingNode()) {
            return newNode;
        }

        if (newNode.isNull() && oldNode.isObject()) {
            return oldNode;
        }

        if (newNode.isArray()) {
            return newNode;
        }

        if (oldNode.isObject() && newNode != null && newNode.isObject()) {
            JsonNode merged = mapper.valueToTree(oldNode);

            newNode.fieldNames().forEachRemaining(key -> {
                JsonNode oldValue = oldNode.get(key);
                JsonNode newValue = newNode.get(key);

                log.debug("key [" + key + "] oldNode [" + oldValue + "] newNode [" + newValue + "]");

                if (oldValue != null && newValue != null) {
                    if (oldValue.equals(newValue)) {
                        ((ObjectNode) merged).set(key, oldValue);
                    } else {
                        JsonNode mergedValue = mergeNodes(oldValue, newValue);
                        ((ObjectNode) merged).set(key, mergedValue);
                    }
                } else if (newValue != null) {
                    ((ObjectNode) merged).set(key, newValue);
                } else {
                    ((ObjectNode) merged).set(key, oldValue);
                }
            });
            log.info("merged node " + merged);
            return merged;
        }

        return newNode != null ? newNode : oldNode;
    }

    public static String trimString(String val, int digit) {
        if (val == null) {
            return null;
        }
        if (digit <= 0 || val.length() <= digit) {
            return val;
        }
        return val.substring(0, digit);
    }

    public static boolean isProductActivation(String applicationId) {
        return PRODUCT_ACTIVATION_PREFIX.equals(StringUtils.left(applicationId, 4));
    }

    public static boolean isAdminActivation(String applicationId) {
        return ADMIN_ACTIVATION_PREFIX.equals(StringUtils.left(applicationId, 4));
    }

    public static String generateRandomDigit(int length) {
        SecureRandom secureRandom = new SecureRandom();
        StringBuilder randomDigit = new StringBuilder(length);

        for (int i = 0; i < length; i++) {
            int randomInt = secureRandom.nextInt(10);
            randomDigit.append(randomInt);
        }
        return randomDigit.toString();

    }

}


