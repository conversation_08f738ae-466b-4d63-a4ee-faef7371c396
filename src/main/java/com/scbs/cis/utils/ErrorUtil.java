package com.scbs.cis.utils;

import com.scbs.cis.model.ErrorModel;

import java.util.ArrayList;
import java.util.List;

public class ErrorUtil {

    public static List<ErrorModel> errorList(ErrorModel errorModel){
        List<ErrorModel> errorModelList = new ArrayList<>();
        errorModelList.add(errorModel);
        return errorModelList;
    }

    public static List<ErrorModel> errorList(List<ErrorModel> list){
        List<ErrorModel> errorModelList = new ArrayList<>();
        errorModelList.addAll(list);
        return errorModelList;
    }

}
