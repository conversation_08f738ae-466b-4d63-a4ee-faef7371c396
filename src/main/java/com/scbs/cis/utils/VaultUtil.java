package com.scbs.cis.utils;//package com.scbs.cis.utils;
//
//import com.scbs.cis.exception.ConfigException;
//import lombok.RequiredArgsConstructor;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.core.env.Environment;
//import org.springframework.stereotype.Component;
//import org.springframework.vault.core.VaultOperations;
//import org.springframework.vault.core.VaultTemplate;
//import org.springframework.vault.support.Ciphertext;
//import org.springframework.vault.support.VaultResponseSupport;
//import javax.annotation.PostConstruct;
//import java.util.HashMap;
//import java.util.Map;
//
//@Component
//@RequiredArgsConstructor
//public class VaultUtil {
//
//    private  final Logger logger = LoggerFactory.getLogger(VaultUtil.class.getName());
//
//    @Value("${spring.application.name}")
//    private String applicationName;
//
//    @Value("${COMPANY}")
//    private String COMPANY;
//
//    @Value("${PROJECT_NAME}")
//    private String PROJECT_NAME;
//
//    @Value("${spring.profiles.active}")
//    private String profilesActive;
//
//    @Value("${cis.vault.config.enable:true}")
//    private boolean vaultConfigEnable;
//
//    private final VaultOperations operations;
//
//    private final VaultTemplate vaultTemplate;
//
//    private final Environment env; //for get env prop
//
//    private Map<String,String> vaultValueMap;
//
//    @PostConstruct
//    private void postConstruct() throws ConfigException {
//        if(vaultConfigEnable){
//            //kv_v2
//            String path = "secret/data/"+COMPANY+ "/" + PROJECT_NAME + "/" + applicationName + "/" + profilesActive;
//            logger.info("Path Vault -> {}.", path);
//            VaultResponseSupport<Map> response = vaultTemplate.read(path,Map.class);
//            if(response!=null){
//                vaultValueMap = (HashMap) response.getData().get("data");
//            }
//            if(vaultValueMap == null){
//                logger.warn("vault data is empty");
//            }
//
//        }else{
//            vaultValueMap=new HashMap<>();
//        }
//
//        // Print log show value
//         String var1 = this.value("PRIVATE_KEY");
//         logger.info("Value from vault : {}.", var1);
//    }
//
//    public String value(String key) throws ConfigException {
//            return vaultValueMap.get(key);
//
////        if(vaultValueMap.containsKey(key)){
////            return transitDecrypt(vaultValueMap.get(key));
////        }else if(env.containsProperty(key)){
////            return env.getProperty(key);
////        }else{
////            if(vaultConfigEnable) {
////                throw new ConfigException(key + " not have setting :" + COMPANY + "/" + PROJECT_NAME + "/" + applicationName + "/" + profilesActive);
////            }else{
////                return StringUtils.EMPTY;
////            }
////        }
//    }
//
//    public String transitDecrypt(String vaultValue) {
//        return StringUtils.trim(operations.opsForTransit().decrypt(this.getVaultTransit(), Ciphertext.of(vaultValue)).asString());
//    }
//
//    private String getVaultTransit(){
//        return COMPANY+"-"+PROJECT_NAME+"-"+applicationName+"-"+profilesActive;
//    }
//
//}
