package com.scbs.cis.utils;

import com.scbs.cis.constants.StatusResponseEnum;
import com.scbs.cis.exception.CISServiceException;
import java.nio.charset.StandardCharsets;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import com.invx.cis.toolbox.logger.Log;
import com.invx.cis.toolbox.logger.LogFactory;

@Component
public class EnDeCryptUtil {

    private final Log log = LogFactory.getLogger(this.getClass());
    public static final String CIPHER_AES_ECB_PKCS5 = "AES/ECB/PKCS5Padding";

    @Value("${SETTRADE_ENCRYPT_KEY}")
    private String settradeAESEncryptionPassphrase;


    public String aesEncryptionToHex(String textToEncrypt) {
        try {

            log.debug("SETTRADE_ENCRYPT_KEY -> {}", settradeAESEncryptionPassphrase);

            // Create AES key from the key bytes
            SecretKeySpec aesKey = new SecretKeySpec(settradeAESEncryptionPassphrase.getBytes(), "AES");

            // Create AES cipher with ECB mode and PKCS7 padding
            Cipher cipher = Cipher.getInstance(CIPHER_AES_ECB_PKCS5); //NOSONAR

            // Encrypt the plain text
            cipher.init(Cipher.ENCRYPT_MODE, aesKey);
            byte[] encryptedBytes = cipher.doFinal(textToEncrypt.getBytes(StandardCharsets.UTF_8));

            // Encode the encrypted bytes to hexadecimal format
            return byteArrayToHexString(encryptedBytes);
        } catch (Exception e) {
            log.error(CommonUtil.convertStackToStingNotLimit(e));
            throw new CISServiceException(HttpStatus.INTERNAL_SERVER_ERROR,
                    StatusResponseEnum.UNEXPECTED_ERROR_OCCURRED);
        }
    }

    private static String byteArrayToHexString(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X", b));
        }
        return sb.toString();
    }

}
