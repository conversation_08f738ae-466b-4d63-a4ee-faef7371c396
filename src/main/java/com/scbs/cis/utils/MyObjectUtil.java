package com.scbs.cis.utils;

import com.google.common.base.CaseFormat;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

@Component
public class MyObjectUtil {

    public static <T>  boolean isEmpty(T t) {  // TODO : check cass need to use @Getter

        if (ObjectUtils.isEmpty(t)) {
            return true;
        }

        Field[] allFields = t.getClass().getDeclaredFields();
        for (Field field : allFields) {
            try {
                Method method = t.getClass().getMethod("get" + CaseFormat.LOWER_CAMEL.to(CaseFormat.UPPER_CAMEL, field.getName()));
                if (!ObjectUtils.isEmpty(method.invoke(t))) {
                    return false;
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return true;
    }

    public static <T> boolean isNotEmpty(T t) { // TODO : check cass need to use @Getter
        return !isEmpty(t);
    }

}
