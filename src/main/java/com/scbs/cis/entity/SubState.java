package com.scbs.cis.entity;

import java.time.LocalDateTime;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@Entity
@NoArgsConstructor
@AllArgsConstructor
public class SubState {

    @Id
    private String id;
    private String taskId;
    private String taskStateId;
    private String subStateName;
    private String subStateStatus;
    private String subStateDetail;
    private String createdBy;
    private LocalDateTime createdDatetime;
    private String updatedBy;
    private LocalDateTime updatedDatetime;
}
